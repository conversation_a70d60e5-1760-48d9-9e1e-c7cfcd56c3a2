<?php
declare(strict_types=1);

namespace App\Constants;

class ProductionCode
{
    //订单类型
    const ORDER_TYPE_PRODUCTION = 1;
    const ORDER_TYPE_ASSEMBLE = 2;

    //---------------------------------------工厂---------------------------------------------
    const FACTORY_CODE_TQ =  'N.164';
    const FACTORY_NAME_TQ =  '中山市天启智能科技有限公司';
    //---------------------------------------MAC/SN------------------------------------------
    //mac/sn使用类型，和分类中mac使用类型对应
    const CODE_USED_TYPE_PRODUCTION = 1;
    const CODE_USED_TYPE_ASSEMBLE = 2;//组装

    const CODE_USED_TYPE_OTHER = 9;

    const SN_USED_TYPE = [
        self::CODE_USED_TYPE_PRODUCTION => '生产订单',
        self::CODE_USED_TYPE_ASSEMBLE => '整机订单',
    ];

    /* 公司购买的mac地址起始号 */
    const START_MAC = '5CF838700000';
    const START_MAC_DEC=102221168508928;

    /* 公司购买的mac地址结束号 */
    const END_MAC = '5CF8387FFFFF';
    const END_MAC_DEC =102221169557503;

    const ORIGIN_TYPE_OF_SELF = 1;//自己购买
    const ORIGIN_TYPE_OF_CUSTOMER = 2;//客户提供

    const ORIGIN_TYPE = [
        self::ORIGIN_TYPE_OF_SELF => '天启',
        self::ORIGIN_TYPE_OF_CUSTOMER => '客户'
    ];

    public static function getOriginTypeByName($input){
        $flipped = array_flip(self::ORIGIN_TYPE);

        // 检查输入是否在映射中
        return $flipped[$input] ?? self::ORIGIN_TYPE_OF_SELF; // 若没有匹配的项，则默认自己数据
    }

    //快递方式
    const EXPRESS_COMPANY = [
        ['id'=>1,'name'=>'顺丰快递','prefix'=>'SF'],
        ['id'=>2,'name'=>'信丰物流','prefix'=>'XF'],
        ['id'=>99,'name'=>'其他方式','prefix'=>'']
    ];

    //日志的缓存标识
    const LOG_KEY_TABLE_DATA = 'production_log_data_change';

    //日志的类型
    //http请求
    const LOG_TYPE_HTTP_REQUEST = 1;
    const LOG_TYPE_CRONTAB = 2;

    const EXCEPTION_SEND_USER = '钟绍源';

    //产品平台
    const SHIPMENT_PLATFORM_ARR = [23,25];

    //---------------------------异常-------------------------
    const EXCEPTION_TYPE_PROUCTION_ORDER = 1;
    const EXCEPTION_TYPE_ASSEMBLE_ORDER = 2;

    //异常状态
    const EXCEPTION_STATUS_WAIT = 1;
    const EXCEPTION_STATUS_DOING = 2;
    const EXCEPTION_STATUS_FINISH = 3;
    const EXCEPTION_STATUS_SUSPEND = 4;
    const EXCEPTION_STATUS = [
        1 => '待处理',
        2 => '处理中',
        3 => '完成',
        4 => '挂起',
    ];

}