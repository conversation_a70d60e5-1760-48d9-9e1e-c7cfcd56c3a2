<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 上午11:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\WorkWx;

use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxDepartmentService;
use App\Core\Services\WorkWx\WorkWxUserService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Psr\Http\Message\ResponseInterface;

/**
 * 企业微信-通讯录管理
 *
 * @AutoController()
 * @WorkWxTokenAnnotation(type="contact")
 */
class ContactController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var WorkWxDepartmentService
     */
    protected $workWxDepartmentService;

    /**
     * @Inject()
     * @var WorkWxUserService
     */
    protected $workWxUserService;

    /**
     * 同步部门列表
     * @return ResponseInterface
     */
    public function syncDepartmentList(): ResponseInterface
    {
        $result = $this->workWxDepartmentService->syncDepartmentList();
        return $this->response->success($result);
    }

    /**
     * 同步成员列表
     * @return ResponseInterface
     */
    public function syncUserList(): ResponseInterface
    {
        $result = $this->workWxUserService->syncUserList();
        return $this->response->success($result);
    }

    /**
     * 获取成员列表
     * @return ResponseInterface
     */
    public function getUserList()
    {
        $result = $this->workWxUserService->getUserList();
        return $this->response->success($result);
    }
}