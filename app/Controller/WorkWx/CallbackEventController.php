<?php

namespace App\Controller\WorkWx;

use App\Annotation\ControllerNameAnnotation;
use App\Annotation\WorkWxTokenAnnotation;
use App\Controller\AbstractController;
use App\Constants\StatusCode;
use App\Core\Services\WorkWx\CallbackEventService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use App\Core\Utils\Weworkapi\WXBizMsgCrypt;
use App\Core\Utils\Response;

/**
 * @ControllerNameAnnotation("企业微信回调")
 * @AutoController()
 */
class CallbackEventController extends AbstractController
{
    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    /**
     * @Inject()
     * @var CallbackEventService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("回调入口")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function index()
    {
        if ($this->request->getMethod() === 'GET'){
            $sVerifyMsgSig = $this->request->input('msg_signature');
            $sVerifyTimeStamp = $this->request->input('timestamp');
            $sVerifyNonce = $this->request->input('nonce');
            $sVerifyEchoStr = $this->request->input('echostr');
            return $this->service->verifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr);
        } else if ($this->request->getMethod() === 'POST') {
            $content = $this->request->all();
            $result = $this->service->handleEvent($content);
            return $this->response->success($result);
        }
    }
}