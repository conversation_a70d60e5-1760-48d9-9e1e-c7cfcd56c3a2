<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/4/3 下午5:07
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\VersionService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("版本管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class VersionController extends BaseController
{
    /**
     * @Inject()
     * @var VersionService
     */
    protected $service;
}