<?php

declare(strict_types=1);

namespace App\Core\Services\Project;

use App\Constants\StatusCode;
use App\Core\Services\BaseService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\IssueRelationModel;
use App\Model\Redmine\IssueSyncRelationsModel;
use App\Model\Redmine\IssuesExtModel;
use App\Model\Redmine\IssueSyncFieldsModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\JournalDetailsModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\CustomValuesModel;
use App\Model\Redmine\ProjectsTrackerModel;
use App\Model\Redmine\UserModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Coroutine;

/**
 * 问题同步服务类
 *
 */
class IssueSyncService extends BaseService
{
    /**
     * @Inject()
     * @var IssueSyncRelationsModel
     */
    protected $relationModel;

    /**
     * @Inject()
     * @var IssueSyncFieldsModel
     */
    protected $syncFieldsModel;

    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * @Inject()
     * @var JournalsModel
     */
    protected $journalsModel;

    /**
     * @Inject()
     * @var JournalDetailsModel
     */
    protected $journalDetailsModel;



    /**
     * 创建问题同步关系
     *
     * @param int $sourceIssueId 源问题ID
     * @param int $targetIssueId 目标问题ID
     * @param string $syncDirection 同步方向
     * @param array $syncFields 需要同步的字段配置
     * @return IssueSyncRelationsModel
     * @throws AppException
     */
    public function createSyncRelation(
        int $sourceIssueId,
        int $targetIssueId,
        string $syncDirection = IssueSyncRelationsModel::SYNC_BIDIRECTIONAL,
        array $syncFields = []
    ): IssueSyncRelationsModel {
        try {
            // 验证问题是否存在
            $sourceIssue = make(IssueModel::class)->find($sourceIssueId);
            $targetIssue = make(IssueModel::class)->find($targetIssueId);

            if (!$sourceIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "源问题不存在");
            }

            if (!$targetIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "目标问题不存在");
            }

            // 检查是否已存在同步关系
            $existingRelation = $this->relationModel
                ->where('source_issue_id', $sourceIssueId)
                ->where('target_issue_id', $targetIssueId)
                ->first();

            if ($existingRelation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系已存在");
            }

            // 获取当前用户信息
            $currentUserId = $this->getCurrentUser();

            // 开启事务
            Db::connection('tchip_redmine')->beginTransaction();

            try {
                // 创建同步关系
                $relation = new IssueSyncRelationsModel();
                $relation->source_issue_id = $sourceIssueId;
                $relation->target_issue_id = $targetIssueId;
                $relation->sync_direction = $syncDirection;
                $relation->is_active = 1;
                $relation->created_by = $currentUserId;
                $relation->updated_by = $currentUserId;
                $relation->created_at = Carbon::now();
                $relation->updated_at = Carbon::now();
                $relation->save();

                // 如果指定了同步字段，创建字段配置
                if (!empty($syncFields)) {
                    $this->createSyncFieldsConfig($relation->id, $syncFields);
                } else {
                    // 使用默认配置
                    $this->createDefaultSyncFieldsConfig($relation->id);
                }


                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('创建同步关系成功', [
                    'relation_id' => $relation->id,
                    'source_issue_id' => $sourceIssueId,
                    'target_issue_id' => $targetIssueId,
                    'sync_direction' => $syncDirection
                ]);

                return $relation;

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('创建同步关系失败', [
                'source_issue_id' => $sourceIssueId,
                'target_issue_id' => $targetIssueId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 更新同步关系配置
     *
     * @param int $relationId 同步关系ID
     * @param array $data 更新数据
     * @return bool
     * @throws AppException
     */
    public function updateSyncRelation(int $relationId, array $data): bool
    {
        try {
            $relation = $this->relationModel->find($relationId);

            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }

            $currentUserId = $this->getCurrentUser();

            // 更新同步关系
            if (isset($data['sync_direction'])) {
                $relation->sync_direction = $data['sync_direction'];
            }

            if (isset($data['is_active'])) {
                $relation->is_active = $data['is_active'];
            }

            $relation->updated_by = $currentUserId;
            $relation->updated_at = Carbon::now();
            $relation->save();

            // 更新同步字段配置
            if (isset($data['sync_fields'])) {
                $this->updateSyncFieldsConfig($relationId, $data['sync_fields']);
            }

             Log::get()->info('更新同步关系成功', [
                'relation_id' => $relationId,
                'data' => $data
            ]);

            return true;

        } catch (\Exception $e) {
            Log::get('debug')->error('更新同步关系失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 删除同步关系
     *
     * @param int $relationId 同步关系ID
     * @return bool
     * @throws AppException
     */
    public function deleteSyncRelation(int $relationId): bool
    {
        try {
            $relation = $this->relationModel->find($relationId);

            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }

            Db::connection('tchip_redmine')->beginTransaction();

            try {
                // 删除同步字段配置
                $this->syncFieldsModel->where('relation_id', $relationId)->delete();

                // 物理删除同步关系
                $relation->delete();

                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('删除同步关系成功', ['relation_id' => $relationId]);

                return true;

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('删除同步关系失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 同步事项变更（递归传播）
     *
     * @param int $issueId 发生变更的问题ID
     * @param array $changedFields 变更的字段
     * @param array $newValues 新值
     * @param array $oldValues 引动变化的旧值
     * @param array $extValues 扩展值
     * @param int $userId 操作用户ID
     * @param array $visited 已处理过的 issue，避免循环
     * @return void
     */
    public function syncIssueChanges(
        int $issueId,
        array $changedFields,
        array $newValues,
        array $oldValues,
        array $extValues,
        int $userId = null,
        array &$visited = []
    ): void {
        // 防止循环引用导致死循环
        if (in_array($issueId, $visited, true)) {
            return;
        }
        $visited[] = $issueId;

        try {
            // 查找直接相关的同步关系（不取全链，只取一层）
            $relations = $this->relationModel
                ->where('is_active', 1)
                ->whereNull('deleted_at')
                ->where(function ($query) use ($issueId) {
                    $query->where('source_issue_id', $issueId)
                        ->orWhere('target_issue_id', $issueId);
                })
                ->get();

            if ($relations->isEmpty()) {
                return;
            }

            foreach ($relations as $relation) {
                // 判断同步方向
                $isSource = $relation->source_issue_id === $issueId;
                $targetIssueId = $isSource ? $relation->target_issue_id : $relation->source_issue_id;

                // 检查同步方向是否允许
                if ($isSource && !$relation->canSyncFromSource()) {
                    continue;
                }
                if (!$isSource && !$relation->canSyncFromTarget()) {
                    continue;
                }

                // 获取需要同步的字段配置
                $syncFields = $this->getSyncFieldsForRelation($relation->id);

                // 过滤出需要同步的字段
                $fieldsToSync = array_intersect($changedFields, $syncFields);
                if (empty($fieldsToSync)) {
                    continue;
                }

                $userId = $userId ?? $this->getCurrentUser();

                // 异步执行同步操作，并在完成后递归触发下游同步
                Coroutine::create(function () use (
                    $targetIssueId,
                    $fieldsToSync,
                    $newValues,
                    $relation,
                    $userId,
                    $oldValues,
                    $extValues,
                    &$visited
                ) {
                    $this->performSync($targetIssueId, $fieldsToSync, $newValues, $relation->id, $userId, $oldValues, $extValues);

                    // 同步完成后，递归触发链上下一个 issue 的同步
                    $this->syncIssueChanges($targetIssueId, $fieldsToSync, $newValues, $oldValues, $extValues, $userId, $visited);
                });
            }
        } catch (\Exception $e) {
            Log::get('debug')->error('同步事项变更失败', [
                'issue_id' => $issueId,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function initIssueSyncValues(int $issueId, array $values, array $extValues)
    {
        // BI允许的字段列表，单参传入符合标准的单字段内容修改，多参传入为前端使用上问题此时需要忽略单参传入项的修改
        $allowedFields = [
            'subject', // 标题， 多参传入，修改时传参参数过多需要比对是否存在不同需要处理
            'description', // 描述，多参传入，修改时传参参数过多需要比对是否存在不同需要处理；需要同时处理description_html和description
            'attachment', // 附件，数组，多参传入，修改时传参参数过多需要比对是否存在不同需要处理
            'status_id', // 状态， 单参传入
            'priority_id', // 优先级， 单参传入
            'watchers', // 关注人，单参数组传入
            'assigned', // 处理人，单参数组传入
            'start_date', // 开始时间，单参传入
            'due_date', // 结束时间，单参传入
            'tracker_id', // 事项类型， 单参传入
        ];

        $syncValues = [];

        foreach ($values as $key => $value) {
            // lock_version 处理
            if ($key === 'lock_version') {
                // $syncValues[$key] = $value;
                continue;
            }

            // 判断普通字段是否允许
            if (in_array($key, $allowedFields, true)) {
                // watchers 和 assigned 只保留数字 ID
                if (in_array($key, ['watchers', 'assigned'], true) && is_array($value)) {
                    $syncValues[$key] = array_filter($value, 'is_numeric');
                } else {
                    $syncValues[$key] = $value;
                }
            }
        }

        // 新传入附件数据处理
        if (!empty($syncValues['attachment'])) {
            $syncValues['attachment'] = array_column($syncValues['attachment'], 'id');
        }

        // 获取旧值
        $oldValues = make(IssueModel::class)
            ->with(['attachment', 'issueAssigned', 'watcher'])
            ->find($issueId)
            ->toArray();
        // 多处理人 assigned 处理
        if (!empty($oldValues['issue_assigned'])) {
            $oldValues['assigned'] = array_column($oldValues['issue_assigned'], 'user_id');
            unset($oldValues['issue_assigned']);
        }
        // 关注人 watchers 处理
        if (!empty($oldValues['watcher'])) {
            $oldValues['watchers'] = array_column($oldValues['watcher'], 'user_id');
            unset($oldValues['watcher']);
        }
        // 附件 attachment 处理
        if (!empty($oldValues['attachment'])) {
            $oldValues['attachment'] = array_column($oldValues['attachment'], 'id');
            unset($oldValues['attachment']);
        }

        // 计算变化字段
        $changedFields = [];
        foreach ($syncValues as $k => $v) {
            if ($this->compareValue($oldValues[$k] ?? null, $v, $k)) {
                $changedFields[] = $k;
            }
        }

        Log::get()->info('同步事项变更,存在变化的字段有：', [
            'issue_id' => $issueId,
            'changed_fields' => $changedFields,
            'old_values' => $oldValues,
            'new_values' => $syncValues
        ]);

        // 调用同步方法
        $this->syncIssueChanges($issueId, $changedFields, $syncValues, $oldValues ,$extValues);

        return $syncValues;
    }

    /**
     * 比较新旧值是否不同
     */
    protected function compareValue($oldValue, $newValue, string $key): bool
    {

        // watchers、assigned：比对 user_id 数组, 附件处理：比对处理后的attachment id 集合
        if (in_array($key, ['watchers', 'assigned', 'attachment'], true)) {
            $oldArr = array_map('intval', $oldValue ?? []);
            $newArr = array_map('intval', $newValue ?? []);
            sort($oldArr);
            sort($newArr);
            return $oldArr !== $newArr;
        }

        // 其他字段：直接对比
        return $oldValue !== $newValue;
    }


    /**
     * 执行同步操作
     *
     * @param int $targetIssueId 目标问题ID
     * @param array $fieldsToSync 需要同步的字段
     * @param array $values 新值
     * @param int $relationId 同步关系ID
     * @param int $userId 操作用户ID
     * @param array $oldValues 旧值
     * @param array $extValues 扩展值
     * @return void
     */
    protected function performSync(int $targetIssueId, array $fieldsToSync, array $values, int $relationId, int $userId, array $oldValues, array $extValues): void
    {
        try {
            $targetIssue = make(IssueModel::class)->find($targetIssueId);

            if (!$targetIssue) {
                Log::get('debug')->error('目标问题不存在', ['issue_id' => $targetIssueId]);
                return;
            }

            Db::connection('tchip_redmine')->beginTransaction();

            try {
                $currentUserId = $userId;

                // 创建日志记录
                $journal = new JournalsModel();
                $journal->journalized_type = 'Issue';
                $journal->journalized_id = $targetIssueId;
                $journal->user_id = $currentUserId;
                $journal->notes = ""; // 根据肉眼观察法，不留空的话会作为评论出现在评论区
                $journal->created_on = Carbon::now('UTC'); // redmine使用的是UTC时区
                $journal->private_notes = 0;
                $journal->save();

                // 定义需要特殊处理的字段
                $specialFields = ['description', 'attachment', 'watchers', 'assigned', 'status_id'];

                // 分离普通字段和特殊字段
                $normalFields = array_diff($fieldsToSync, $specialFields);
                $specialFieldsToSync = array_intersect($fieldsToSync, $specialFields);

                // 处理普通字段
                foreach ($normalFields as $field) {
                    if (isset($values[$field])) {
                        $this->syncNormalField($targetIssue, $field, $values[$field], $journal->id, $relationId);
                    }
                }

                // 处理特殊字段
                foreach ($specialFieldsToSync as $field) {
                    if (isset($values[$field])) {
                        $this->syncSpecialField($targetIssue, $field, $values[$field], $journal->id, $relationId, $oldValues, $extValues);
                    }
                }

                // 更新问题的修改时间
                $targetIssue->updated_on = Carbon::now();
                $targetIssue->save();

                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('同步操作完成', [
                    'target_issue_id' => $targetIssueId,
                    'synced_fields' => $fieldsToSync,
                    'relation_id' => $relationId
                ]);

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('执行同步操作失败', [
                'target_issue_id' => $targetIssueId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建变更记录详情
     *
     * @param int $journalId 日志ID
     * @param string $fieldKey 字段名
     * @param mixed $oldValue 旧值
     * @param mixed $newValue 新值
     * @param int $relationId 同步关系ID
     * @return void
     */
    private function createJournalDetail(int $journalId, string $fieldKey, $oldValue, $newValue, int $relationId): void
    {
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = $fieldKey;

        // 处理不同数据类型的值
        if (is_array($oldValue)) {
            $journalDetail->old_value = json_encode($oldValue);
        } else {
            $journalDetail->old_value = $oldValue;
        }

        if (is_array($newValue)) {
            $journalDetail->value = json_encode($newValue);
        } else {
            $journalDetail->value = $newValue;
        }

        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();
    }

    /**
     * 同步普通字段
     *
     * @param IssueModel $targetIssue 目标问题
     * @param string $field 字段名
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncNormalField(IssueModel $targetIssue, string $field, $newValue, int $journalId, int $relationId): void
    {
        $oldValue = $targetIssue->{$field};

        // 更新问题字段
        $targetIssue->{$field} = $newValue;

        // 记录变更详情
        $this->createJournalDetail($journalId, $field, $oldValue, $newValue, $relationId);

        Log::get('debug')->error('同步普通字段成功', [
            'target_issue_id' => $targetIssue->id,
            'field' => $field,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步特殊字段
     *
     * @param IssueModel $targetIssue 目标问题
     * @param string $field 字段名
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @param array $oldValues 旧值
     * @param array $extValues 扩展值
     * @return void
     */
    protected function syncSpecialField(IssueModel $targetIssue, string $field, $newValue, int $journalId, int $relationId, array $oldValues, array $extValues): void
    {
        switch ($field) {
            case 'description':
                $this->syncDescriptionField($targetIssue, $newValue, $journalId, $relationId, $extValues);
                break;
            case 'attachment':
                $this->syncAttachmentField($targetIssue, $newValue, $journalId, $relationId);
                break;
            case 'watchers':
                $this->syncWatchersField($targetIssue, $newValue, $journalId, $relationId);
                break;
            case 'assigned':
                $this->syncAssignedField($targetIssue, $newValue, $journalId, $relationId);
                break;
            case 'status_id':
                $this->syncStatusField($targetIssue, $field, $newValue, $journalId, $relationId, $oldValues);
                break;
            default:
                // 如果不是特殊字段，使用普通字段处理方式
                $this->syncNormalField($targetIssue, $field, $newValue, $journalId, $relationId);
                break;
        }
    }

    /**
     * 同步描述字段
     * 需要同时处理 description 和 description_html
     *
     * @param IssueModel $targetIssue 目标问题
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncDescriptionField(IssueModel $targetIssue, $newValue, int $journalId, int $relationId, array $extValues): void
    {
        $oldValue = $targetIssue->description;

        // 更新描述字段
        $targetIssue->description = $newValue;

        // 同时更新description_html字段
        $targetIssueExt = make(IssuesExtModel::class)::query()->where('issue_id', $targetIssue->id)->first();
        $targetIssueExt->description_html = $extValues['description_html'] ?? '';
        $targetIssueExt->save();

        // 记录变更详情
        $this->createJournalDetail($journalId, 'description', $oldValue, $newValue, $relationId);

        Log::get()->info('同步描述字段成功', [
            'target_issue_id' => $targetIssue->id,
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步附件字段
     * 处理附件数组类型数据
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（附件数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncAttachmentField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // 根据同步关系id获取 附件字段需要同同步的开始时间
        $relation = $this->syncFieldsModel::query()->where('relation_id', $relationId)
            ->where('field_key', 'attachment')
            ->first();
        $startTime = $relation->created_at->copy()->setTimezone('UTC');

        // 获取目标 issue 当前已有附件，历史附件不做处理
        $currentAttachments = AttachmentModel::query()
            ->where('container_type', 'Issue')
            ->where('container_id', $targetIssue->id)
            ->where('created_on', '>=', $startTime)
            ->get();


        $oldAttachmentIds = $currentAttachments->pluck('id')->toArray();
        $newAttachmentIds = $newValue;

        // 历史已有附件不做处理
        $newAttachmentIds = AttachmentModel::query()
            ->whereIn('id', $newAttachmentIds)
            ->where('created_on', '>=', $startTime)
            ->pluck('id')->toArray();

        // 求差集
        $toAdd = array_diff($newAttachmentIds, $oldAttachmentIds);
        $toRemove = array_diff($oldAttachmentIds, $newAttachmentIds);

        // 记录变更详情
        $this->createJournalDetail($journalId, 'attachment', $oldAttachmentIds, $newAttachmentIds, $relationId);

        // 删除多余的附件
        if (!empty($toRemove)) {
            AttachmentModel::query()
                ->where('container_type', 'Issue')
                ->where('container_id', $targetIssue->id)
                ->whereIn('id', $toRemove)
                ->delete();
        }

        // 添加新的附件（从原始附件表里复制）
        if (!empty($toAdd)) {
            $attachments = AttachmentModel::query()
                ->whereIn('id', $toAdd)
                ->get();

            $attachmentData = [];
            foreach ($attachments as $attachment) {
                $attachmentData[] = [
                    'container_id'   => $targetIssue->id,
                    'container_type' => 'Issue',
                    'filename'       => $attachment->filename,
                    'disk_filename'  => $attachment->disk_filename,
                    'filesize'       => $attachment->filesize,
                    'content_type'   => $attachment->content_type,
                    'digest'         => $attachment->digest,
                    'downloads'      => 0,
                    'author_id'      => $attachment->author_id,
                    'created_on'     => Carbon::now('UTC'),
                    'description'    => $attachment->description,
                    'disk_directory' => $attachment->disk_directory,
                ];
            }

            if (!empty($attachmentData)) {
                AttachmentModel::insert($attachmentData);
            }
        }

        Log::get()->info('同步附件字段成功', [
            'target_issue_id' => $targetIssue->id,
            'old_attachments' => $oldAttachmentIds,
            'new_attachments' => $newAttachmentIds,
            'added'           => $toAdd,
            'removed'         => $toRemove,
            'relation_id'     => $relationId,
        ]);
    }

    /**
     * 同步关注人字段
     * 处理用户ID数组
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（用户ID数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncWatchersField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // // 获取当前关注人
        // $oldWatcherIds = make(WatcherService::class)->getWatcherIds($targetIssue->id);

        // 过滤并确保新值都是数字ID
        $newWatcherIds = array_filter($newValue, 'is_numeric');
        $newWatcherIds = array_map('intval', $newWatcherIds);

        // 记录变更详情
        // $this->createJournalDetail($journalId, 'watchers', $oldWatcherIds, $newWatcherIds, $relationId);

        // 更新关注人
        make(WatcherService::class)->updateWatchers($targetIssue->id, $newWatcherIds);

        Log::get()->info('同步关注人字段成功', [
            'target_issue_id' => $targetIssue->id,
            // 'old_watchers' => count($oldWatcherIds),
            'new_watchers' => count($newWatcherIds),
            'now_watchers' => $newWatcherIds,
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步处理人字段
     * 处理用户ID数组
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（用户ID数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncAssignedField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // 获取当前处理人
        $currentAssigned = collect($targetIssue->issueAssigned ?? []);
        $oldAssignedIds = $currentAssigned->toArray();
        $oldAssignedIds = array_column($oldAssignedIds, 'user_id');

        // 过滤并确保新值都是数字ID
        $newAssignedIds = array_filter($newValue, 'is_numeric');
        $newAssignedIds = array_map('intval', $newAssignedIds);

        $projectMemberIds = make(MemberService::class)->getProjectMembers($targetIssue->project_id);
        $projectMemberIds = array_column($projectMemberIds, 'user_id');

        // 20250826 15：30 去除不在对应项目成员中的处理人
        $newAssignedIds = array_intersect($newAssignedIds, $projectMemberIds);

        if (count($newAssignedIds) <= 1) {
            // 20250821 目前的redmine处理人日志只关注issue表原assigned_to_id字段
            make(\App\Core\Services\Project\IssueAssignedService::class)->updateAssigned($targetIssue->id, $newAssignedIds);
            $oldAssignedToId = $targetIssue->assigned_to_id;
            $targetIssue->assigned_to_id = $newAssignedIds[0] ?? null;
            $targetIssue->save();
            // // 记录变更详情
            $this->createJournalDetail($journalId, 'assigned_to_id', $oldAssignedToId, $targetIssue->assigned_to_id , $relationId);
        } else {
            make(\App\Core\Services\Project\IssueAssignedService::class)->updateAssigned($targetIssue->id, $newAssignedIds);
        }

        Log::get()->info('同步处理人字段成功', [
            'target_issue_id' => $targetIssue->id,
            'old_assigned' => count($oldAssignedIds),
            'new_assigned' => count($newAssignedIds),
            'relation_id' => $relationId
        ]);
    }


    /**
     * @param IssueModel
     * @param string $field
     * @param $newValue
     * @param int $journalId
     * @param int $relationId
     * @return void
     */
    protected function syncStatusField(IssueModel $targetIssue, string $field, $newValue, int $journalId, int $relationId, array $oldValues): void
    {
        $targetIssueProjectId = $targetIssue->project_id;

        // 20250825 只有在tracker_id一致并且工作流模板一致的情况下才进行同步
        if ($targetIssue->tracker_id != $oldValues['tracker_id']) {
            Log::get('debug')->error('事项类型不一致，不进行状态同步', [
                'target_issue_id' => $targetIssue->id,
                'target_issue_tracker_id' => $targetIssue->tracker_id,
                'old_tracker_id' => $oldValues['tracker_id'],
            ]);
            return;
        } else {
            $targetIssueTemplateId = ProjectsTrackerModel::query()
                ->where('project_id', $targetIssue->project_id)
                ->where('tracker_id', $targetIssue->tracker_id)
                ->value('template_id');

            $oldValuesTemplateId = ProjectsTrackerModel::query()
                ->where('project_id', $oldValues['project_id'])
                ->where('tracker_id', $oldValues['tracker_id'])
                ->value('template_id');

            if ($targetIssueTemplateId != $oldValuesTemplateId) {
                Log::get('debug')->error('工作流模板不一致，不进行状态同步', [
                    'target_issue_id' => $targetIssue->id,
                    'target_issue_project_id' => $targetIssueProjectId,
                    'old_project_id' => $oldValues['project_id'],
                ]);
                return;
            }
        }

        // tapd理念：如果目标项目中不存在对应的status的newValue，跳过处理
        $tagetIssueStatus = make(CustomWorkflowService::class)->getInitialStatuses($targetIssueProjectId, $targetIssue->tracker_id);
        $targetIssueStatusNames = array_column($tagetIssueStatus, 'name', 'id');
        $tagetIssueStatusIds = array_column($tagetIssueStatus, 'id');
        if (!in_array($newValue, $tagetIssueStatusIds)) {
            Log::get('debug')->error('目标项目中不存在对应的status', [
                'target_issue_id' => $targetIssue->id,
                'new_status_id' => $newValue,
                'target_project_id' => $targetIssueProjectId,
                '$targetIssueStatusNames' => $targetIssueStatusNames,
            ]);
            return;
        }

        // 如果不是特殊字段，使用普通字段处理方式
        $this->syncNormalField($targetIssue, $field, $newValue, $journalId, $relationId);
    }

    /**
     * 查找活跃的同步关系
     *
     * @param int $issueId
     * @return Collection
     */
    protected function findActiveSyncRelations(int $issueId): Collection
    {
        return $this->relationModel
            ->where('is_active', 1)
            ->whereNull('deleted_at')
            ->where(function ($query) use ($issueId) {
                $query->where('source_issue_id', $issueId)
                    ->orWhere('target_issue_id', $issueId);
            })
            ->get();
    }

    /**
     * 获取同步关系的字段配置
     *
     * @param int $relationId
     * @return array
     */
    protected function getSyncFieldsForRelation(int $relationId): array
    {
        $fields = $this->syncFieldsModel
            ->where('relation_id', $relationId)
            ->where('is_enabled', 1)
            ->orderBy('sort_order', 'asc')
            ->pluck('field_key')
            ->toArray();
            
        // 如果没有特定配置，使用全局配置
        if (empty($fields)) {
            $fields = $this->syncFieldsModel
                ->whereNull('relation_id')
                ->where('is_enabled', 1)
                ->orderBy('sort_order', 'asc')
                ->pluck('field_key')
                ->toArray();
        }
        
        // 如果还是没有，使用默认配置
        if (empty($fields)) {
            $fields = array_keys(array_filter(IssueSyncFieldsModel::$defaultSyncFields, function ($config) {
                return $config['enabled'] ?? false;
            }));
        }
        
        return $fields;
    }

    /**
     * 创建同步字段配置
     *
     * @param int $relationId
     * @param array $fields
     * @return void
     */
    protected function createSyncFieldsConfig(int $relationId, array $fields): void
    {
        foreach ($fields as $index => $field) {
            $syncField = new IssueSyncFieldsModel();
            $syncField->relation_id = $relationId;
            
            if (is_array($field)) {
                $syncField->field_key = $field['key'];
                $syncField->field_name = $field['name'] ?? $field['key'];
                $syncField->is_enabled = $field['enabled'] ?? 1;
                $syncField->sort_order = $field['order'] ?? ($index + 1) * 10;
                $syncField->field_config = $field['config'] ?? null;
                $syncField->description = $field['description'] ?? null;
            } else {
                $syncField->field_key = $field;
                $syncField->field_name = IssueSyncFieldsModel::$defaultSyncFields[$field]['name'] ?? $field;
                $syncField->is_enabled = 1;
                $syncField->sort_order = ($index + 1) * 10;
            }
            
            $syncField->created_at = Carbon::now();
            $syncField->updated_at = Carbon::now();
            $syncField->save();
        }
    }

    /**
     * 创建默认同步字段配置
     *
     * @param int $relationId
     * @return void
     */
    protected function createDefaultSyncFieldsConfig(int $relationId): void
    {
        foreach (IssueSyncFieldsModel::$defaultSyncFields as $key => $config) {
            if ($config['enabled']) {
                $syncField = new IssueSyncFieldsModel();
                $syncField->relation_id = $relationId;
                $syncField->field_key = $key;
                $syncField->field_name = $config['name'];
                $syncField->is_enabled = 1;
                $syncField->sort_order = $config['order'];
                $syncField->created_at = Carbon::now();
                $syncField->updated_at = Carbon::now();
                $syncField->save();
            }
        }
    }

    /**
     * 更新同步字段配置
     *
     * @param int $relationId
     * @param array $fields
     * @return void
     */
    protected function updateSyncFieldsConfig(int $relationId, array $fields): void
    {
        // 删除现有配置
        $this->syncFieldsModel->where('relation_id', $relationId)->delete();
        
        // 创建新配置
        $this->createSyncFieldsConfig($relationId, $fields);
    }

    /**
     * 根据事项ID获取同步关系列表
     *
     * @param int|null $sourceIssueId 源事项ID
     * @param int|null $targetIssueId 目标事项ID
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getSyncRelationListByIssue(?int $sourceIssueId, ?int $targetIssueId, int $page = 1, int $limit = 20): array
    {
        try {
            $query = $this->relationModel->query()
                ->whereNull('deleted_at')
                ->with(['sourceIssue', 'targetIssue', 'syncFields']);
            
            // 根据传入的参数构建查询条件
            if ($sourceIssueId && $targetIssueId) {
                // 如果同时提供了两个ID，查询这两个事项之间的同步关系
                $query->where(function ($q) use ($sourceIssueId, $targetIssueId) {
                    $q->where(function ($q1) use ($sourceIssueId, $targetIssueId) {
                        $q1->where('source_issue_id', $sourceIssueId)
                           ->where('target_issue_id', $targetIssueId);
                    })->orWhere(function ($q2) use ($sourceIssueId, $targetIssueId) {
                        $q2->where('source_issue_id', $targetIssueId)
                           ->where('target_issue_id', $sourceIssueId);
                    });
                });
            } elseif ($sourceIssueId) {
                // 只提供了源事项ID，查询该事项作为源或目标的所有同步关系
                $query->where(function ($q) use ($sourceIssueId) {
                    $q->where('source_issue_id', $sourceIssueId)
                      ->orWhere('target_issue_id', $sourceIssueId);
                });
            } elseif ($targetIssueId) {
                // 只提供了目标事项ID，查询该事项作为源或目标的所有同步关系
                $query->where(function ($q) use ($targetIssueId) {
                    $q->where('source_issue_id', $targetIssueId)
                      ->orWhere('target_issue_id', $targetIssueId);
                });
            }
            
            // 分页
            $total = $query->count();
            $offset = ($page - 1) * $limit;
            
            $list = $query
                ->orderBy('id', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();
            
            // 处理数据，标识当前事项是源还是目标
            $referenceId = $sourceIssueId ?: $targetIssueId;
            $list = $list->map(function ($item) use ($referenceId) {
                if ($referenceId) {
                    $item->is_source = $item->source_issue_id == $referenceId;
                    $item->related_issue = $item->is_source ? $item->targetIssue : $item->sourceIssue;
                }
                $item->field_keys = $item->syncFields->pluck('field_key')->toArray();
                return $item;
            });

            $currentIsssueIsSource = null;
            if ($list->isEmpty()) {
                $currentIsssueIsSource = $this->judgeIssueIsSource($sourceIssueId, $targetIssueId);
            }

            return [
                'current_issue_is_source' => $currentIsssueIsSource,
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
        } catch (\Exception $e) {
            Log::get('debug')->error('根据事项ID获取同步关系列表失败', [
                'source_issue_id' => $sourceIssueId,
                'target_issue_id' => $targetIssueId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    public function judgeIssueIsSource(int $sourceIssueId, int $targetIssueId): bool
    {
        $query = make(IssueRelationModel::class)::query();

        // 构建查询条件
        if ($sourceIssueId && $targetIssueId) {
            $query->where(function ($q) use ($sourceIssueId, $targetIssueId) {
                $q->where(function ($q1) use ($sourceIssueId, $targetIssueId) {
                    $q1->where('issue_from_id', $sourceIssueId)
                        ->where('issue_to_id', $targetIssueId);
                })->orWhere(function ($q2) use ($sourceIssueId, $targetIssueId) {
                    $q2->where('issue_from_id', $targetIssueId)
                        ->where('issue_to_id', $sourceIssueId);
                });
            });
        } elseif ($sourceIssueId) {
            $query->where(function ($q) use ($sourceIssueId) {
                $q->where('issue_from_id', $sourceIssueId)
                    ->orWhere('issue_to_id', $sourceIssueId);
            });
        } elseif ($targetIssueId) {
            $query->where(function ($q) use ($targetIssueId) {
                $q->where('issue_from_id', $targetIssueId)
                    ->orWhere('issue_to_id', $targetIssueId);
            });
        }

        // 取第一条记录判断
        $relation = $query->first();

        if (!$relation) {
            return false; // 没有找到记录，默认返回 false
        }

        // 如果 sourceIssueId 是查询结果的源，返回 true，否则 false
        return $relation->issue_from_id === $sourceIssueId;
    }


    /**
     * 批量获取同步关系
     * 
     * @param int $sourceIssueId 源事项ID
     * @param array $targetIssueIds 目标事项ID数组
     * @return array
     */
    public function getBatchSyncRelations(int $sourceIssueId, array $targetIssueIds): array
    {
        try {
            // 查询所有相关的同步关系
            $relations = $this->relationModel
                ->whereNull('deleted_at')
                ->where(function ($q) use ($sourceIssueId, $targetIssueIds) {
                    $q->where(function ($q1) use ($sourceIssueId, $targetIssueIds) {
                        // 源事项作为源，目标事项在列表中
                        $q1->where('source_issue_id', $sourceIssueId)
                           ->whereIn('target_issue_id', $targetIssueIds);
                    })->orWhere(function ($q2) use ($sourceIssueId, $targetIssueIds) {
                        // 源事项作为目标，目标事项在列表中作为源
                        $q2->where('target_issue_id', $sourceIssueId)
                           ->whereIn('source_issue_id', $targetIssueIds);
                    });
                })
                ->with(['sourceIssue', 'targetIssue', 'syncFields'])
                ->get();
            
            // 构建以目标事项ID为键的结果映射
            $resultMap = [];
            foreach ($targetIssueIds as $targetId) {
                $resultMap[$targetId] = null;
            }
            
            // 处理查询结果
            foreach ($relations as $relation) {
                $targetId = null;
                $syncInfo = [];
                
                // 判断同步方向
                if ($relation->source_issue_id == $sourceIssueId) {
                    // 源事项是父事项
                    $targetId = $relation->target_issue_id;
                    $syncInfo = [
                        'relation_id' => $relation->id,
                        'sync_direction' => $relation->sync_direction,
                        'is_source' => true,
                        'is_active' => $relation->is_active,
                        'field_keys' => $relation->syncFields->pluck('field_key')->toArray(),
                    ];
                } else {
                    // 源事项是子事项
                    $targetId = $relation->source_issue_id;
                    $syncInfo = [
                        'relation_id' => $relation->id,
                        'sync_direction' => $relation->sync_direction,
                        'is_source' => false,
                        'is_active' => $relation->is_active,
                        'field_keys' => $relation->syncFields->pluck('field_key')->toArray(),
                    ];
                }
                
                if (in_array($targetId, $targetIssueIds)) {
                    $resultMap[$targetId] = $syncInfo;
                }
            }
            
            return $resultMap;
            
        } catch (\Exception $e) {
            Log::get('debug')->error('批量获取同步关系失败', [
                'source_issue_id' => $sourceIssueId,
                'target_issue_ids' => $targetIssueIds,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 获取当前用户
     *
     * @return int|null
     */
    protected function getCurrentUser(): ?int
    {
        try {
            return getRedmineUserId();
        } catch (\Exception $e) {
            Log::get('debug')->error('获取当前用户失败', ['error' => $e->getMessage()]);
        }
        
        return null;
    }

}
