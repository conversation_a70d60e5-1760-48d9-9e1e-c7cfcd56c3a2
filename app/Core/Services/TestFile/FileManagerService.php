<?php
declare(strict_types=1);

namespace App\Core\Services\TestFile;

use App\Core\Services\BusinessService;
use App\Model\TestFile\FileSyncModel;
use App\Model\TestFile\FileProductModel;
use App\Model\TestFile\TestRecordModel;
use App\Model\TestFile\AgingTestResultModel;
use App\Model\TestFile\FactoryTestResultModel;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Carbon\Carbon;

class FileManagerService extends BusinessService
{
    /**
     * 获取文件列表（支持测试结果筛选）
     */
    public function getFileList(array $params): array
    {
        $page = (int) ($params['page'] ?? 1);
        $pageSize = (int) ($params['page_size'] ?? 20);
        $fileType = $params['file_type'] ?? null; // aging_test, factory_test, other
        
        $query = FileSyncModel::query();
        
        // 添加基础筛选条件
        if (!empty($params['product'])) {
            $query->where('product', $params['product']);
        }
        
        if (!empty($params['sn'])) {
            $query->where('sn', $params['sn']);
        }
        
        if (!empty($params['test_type'])) {
            $query->where('test_type', $params['test_type']);
        }
        
        // 文件类型筛选
        if (!empty($fileType)) {
            $query->where('file_type', $fileType);
        }
        
        // 新增：支持按测试日期筛选（用于文件夹内的文件展示）
        if (!empty($params['test_datetime'])) {
            $query->where('test_datetime', $params['test_datetime']);
        }
        
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $query->whereBetween('file_mtime', [
                Carbon::parse($params['start_date'])->startOfDay(),
                Carbon::parse($params['end_date'])->endOfDay()
            ]);
        }
        
        // 老化测试特有筛选条件
        if ($fileType === 'aging_test' && !empty($params['aging_filters'])) {
            $agingFilters = $params['aging_filters'];
            $fileSyncIds = $this->filterAgingTestFiles($agingFilters);
            if (!empty($fileSyncIds)) {
                $query->whereIn('id', $fileSyncIds);
            }
        }
        
        // 厂测特有筛选条件
        if ($fileType === 'factory_test' && !empty($params['factory_filters'])) {
            $factoryFilters = $params['factory_filters'];
            $fileSyncIds = $this->filterFactoryTestFiles($factoryFilters);
            if (!empty($fileSyncIds)) {
                $query->whereIn('id', $fileSyncIds);
            }
        }
        
        // 排除已删除的文件
        $query->where('sync_status', '!=', 3);
        
        // 使用 paginate 方法
        $result = $query->orderBy('id', 'desc')
            ->paginate($pageSize, ['*'], 'page', $page);
        
        // 加载关联的测试结果数据
        $list = $result->items();
        if ($fileType === 'aging_test') {
            $this->loadAgingTestResults($list);
        } elseif ($fileType === 'factory_test') {
            $this->loadFactoryTestResults($list);
        }
        
        return [
            'list' => $list,
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'page_size' => $result->perPage(),
            'total_page' => $result->lastPage()
        ];
    }
    
    /**
     * 获取文件详情
     */
    public function getFileById(int $id): ?array
    {
        $file = FileSyncModel::find($id);
        return $file ? $file->toArray() : null;
    }
    
    /**
     * 删除文件
     */
    public function deleteFile(int $id): bool
    {
        $file = FileSyncModel::find($id);
        
        if (!$file) {
            return false;
        }
        
        // 标记为已删除
        $file->sync_status = 3;
        $file->save();
        
        // 删除实际文件
        if (file_exists($file->dst_path)) {
            @unlink($file->dst_path);
        }
        
        return true;
    }
    
    /**
     * 下载文件
     */
    public function downloadFile(int $id, ResponseInterface $response)
    {
        $file = FileSyncModel::find($id);
        
        if (!$file || !file_exists($file->dst_path)) {
            throw new \Exception('文件不存在');
        }
        
        // 设置下载响应头
        return $response->withHeader('Content-Type', 'application/octet-stream')
                       ->withHeader('Content-Disposition', 'attachment; filename="' . $file->filename . '"')
                       ->withHeader('Cache-Control', 'no-cache, private')
                       ->withBody(new \Hyperf\HttpMessage\Stream\SwooleFileStream($file->dst_path));
    }
    
    /**
     * 获取树形目录数据（支持分页、排序、过滤）
     * @param string $node 节点路径
     * @param int $depth 深度
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param string $sort 排序字段
     * @param string $order 排序方式
     * @param int $limit 每页数量
     * @param int $page 页码
     * @param bool $forTree 是否为树形组件加载
     * @param string $search 搜索关键词
     * @param string $fileType 文件类型
     * @param array $agingFilters 老化测试筛选条件
     * @param array $factoryFilters 厂测筛选条件
     * @return array
     */
    public function getTreeData(
        string $node = '',
        int $depth = 1,
        array $filter = [],
        array $op = [],
        string $sort = '',
        string $order = 'ASC',
        int $limit = 50,
        int $page = 1,
        bool $forTree = false,
        string $search = '',
        string $fileType = '',
        array $agingFilters = [],
        array $factoryFilters = []
    ): array
    {
        $start = null;
        $end = null;
        if (isset($filter['start_date']) && isset($filter['end_date'])) {
            // 确保格式合法并取当天起止
            $start = Carbon::parse($filter['start_date'])->startOfDay()->toDateTimeString(); // 2025-09-25 00:00:00
            $end   = Carbon::parse($filter['end_date'])->endOfDay()->toDateTimeString(); // 2025-09-26 23:59:59
            unset($filter['start_date'], $filter['end_date']);
        }

        ///////////// 首层获取产品列表
        if (empty($node)) {
            // 获取根节点（产品列表）
            $query = Db::table('file_sync')
                ->select('product')
                ->where('sync_status', '!=', 3);
            
            // 应用文件类型筛选
            if (!empty($fileType)) {
                $query->where('file_type', $fileType);
            }
            
            // 应用老化测试或厂测筛选
            $fileSyncIds = $this->applyTestFilters($fileType, $agingFilters, $factoryFilters);
            if ($fileSyncIds !== null) {
                if (empty($fileSyncIds)) {
                    // 没有匹配的文件
                    return $this->formatResponse([], 0, $page, $limit);
                }
                $query->whereIn('id', $fileSyncIds);
            }
            
            $query->groupBy('product');
            
            // 应用搜索
            if (!empty($search)) {
                $query->where('product', 'LIKE', "%{$search}%");
            }
            
            $sort = !empty($sort) ? $sort : 'product';
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 分页处理
            if ($forTree) {
                $products = $query->orderBy($sort, $order)->get();
                $total = count($products);
            } else {
                $result = $query->orderBy($sort, $order)->paginate($limit, ['*'], 'page', $page);
                $products = $result->items();
                $total = $result->total();
            }


            
            $list = [];
            foreach ($products as $product) {
                // 产品节点的计数需要考虑所有筛选条件
                $countQuery = Db::table('file_sync')
                    ->where('product', $product->product)
                    ->where('sync_status', '!=', 3);
                
                // 应用所有筛选条件到计数查询
                if (!empty($filter['sn'])) {
                    $countQuery->where('sn', 'LIKE', '%' . $filter['sn'] . '%');
                }
                
                // 应用日期筛选
                if ($start && $end) {
                    $countQuery->whereDate('file_mtime', '>=', $start)
                              ->whereDate('file_mtime', '<=', $end);
                }
                
                // 应用文件类型筛选
                if (!empty($fileType)) {
                    $countQuery->where('file_type', $fileType);
                }
                
                // 应用测试筛选
                if ($fileSyncIds !== null && !empty($fileSyncIds)) {
                    $countQuery->whereIn('id', $fileSyncIds);
                }

                $count = $countQuery->count();
                    
                $list[] = [
                    'id' => $product->product,
                    'label' => $product->product,
                    'type' => 'product',
                    'children_count' => $count,
                    'isLeaf' => false
                ];
            }

            return $this->formatResponse($list, $total, $page, $limit);
        }
        
        // 解析节点路径
        $parts = explode('/', $node);

        ///////////// 第二层获取SN列表
        if (count($parts) == 1) {
            // 获取SN列表
            $product = $parts[0];
            $query = Db::table('file_sync')
                ->select('sn')
                ->where('product', $product)
                ->where('sync_status', '!=', 3)
                ->groupBy('sn');
            
            // 应用搜索
            if (!empty($search)) {
                $query->where('sn', 'LIKE', "%{$search}%");
            }
            
            // 使用 buildparams 处理查询参数
            $sort = !empty($sort) ? $sort : 'sn';
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
             // 应用老化测试或厂测筛选
            $fileSyncIds = $this->applyTestFilters($fileType, $agingFilters, $factoryFilters);
            if ($fileSyncIds !== null) {
                if (empty($fileSyncIds)) {
                    // 没有匹配的文件
                    return $this->formatResponse([], 0, $page, $limit);
                }
                $query->whereIn('id', $fileSyncIds);
            }

            // 分页处理
            if ($forTree) {
                $sns = $query->orderBy($sort, $order)->get();
                $total = count($sns);
            } else {
                $result = $query->orderBy($sort, $order)->paginate($limit, ['*'], 'page', $page);
                $sns = $result->items();
                $total = $result->total();
            }
            
            $list = [];
            foreach ($sns as $sn) {
                $countQuery = Db::table('file_sync')
                    ->where('product', $product)
                    ->where('sn', $sn->sn)
                    ->where('sync_status', '!=', 3);
                
                // 应用日期筛选到计数
                if ($start && $end) {
                    $countQuery->whereDate('file_mtime', '>=', $start)
                              ->whereDate('file_mtime', '<=', $end);
                }
                
                $count = $countQuery->count();
                    
                $list[] = [
                    'id' => $product . '/' . $sn->sn,
                    'label' => $sn->sn,
                    'type' => 'sn',
                    'children_count' => $count,
                    'isLeaf' => false
                ];
            }
            
            return $this->formatResponse($list, $total, $page, $limit);
        }

        /////////// 第三层获取日期文件夹列表
        if (count($parts) == 2) {
            // 获取日期文件夹列表
            $product = $parts[0];
            $sn = $parts[1];
            
            $query = Db::table('file_sync')
                ->select('test_datetime')
                ->where('product', $product)
                ->where('sn', $sn)
                ->where('sync_status', '!=', 3)
                ->groupBy('test_datetime');
            
            // 应用搜索
            if (!empty($search)) {
                $query->where('test_datetime', 'LIKE', "%{$search}%");
            }
            
            // 处理日期筛选
            if ($start && $end) {

                $query->whereBetween('file_mtime', [$start, $end]);
                // 移除已处理的筛选条件，避免 buildparams 重复处理
                unset($filter['start_date'], $filter['end_date']);
            }
            
            // 使用 buildparams 处理查询参数（日期默认倒序）
            $sort = !empty($sort) ? $sort : 'test_datetime';
            $order = !empty($sort) ? $order : 'DESC';
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
             // 应用老化测试或厂测筛选
            $fileSyncIds = $this->applyTestFilters($fileType, $agingFilters, $factoryFilters);
            if ($fileSyncIds !== null) {
                if (empty($fileSyncIds)) {
                    // 没有匹配的文件
                    return $this->formatResponse([], 0, $page, $limit);
                }
                $query->whereIn('id', $fileSyncIds);
            }

            // 分页处理
            if ($forTree) {
                $dates = $query->orderBy($sort, $order)->get();
                $total = count($dates);
            } else {
                $result = $query->orderBy($sort, $order)->paginate($limit, ['*'], 'page', $page);
                $dates = $result->items();
                $total = $result->total();
            }
            
            $list = [];
            foreach ($dates as $date) {
                $countQuery = Db::table('file_sync')
                    ->where('product', $product)
                    ->where('sn', $sn)
                    ->where('test_datetime', $date->test_datetime)
                    ->where('sync_status', '!=', 3);
                
                $count = $countQuery->count();
                    
                $list[] = [
                    'id' => $product . '/' . $sn . '/' . $date->test_datetime,
                    'label' => $date->test_datetime,
                    'type' => 'date',
                    'children_count' => $count,
                    'isLeaf' => true
                ];
            }
            
            return $this->formatResponse($list, $total, $page, $limit);
        }
        
        return $this->formatResponse([], 0, $page, $limit);
    }
    
    /**
     * 格式化响应数据
     */
    protected function formatResponse($list, $total, $page, $limit)
    {
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $limit,
            'total_page' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取统计数据
     */
    public function getStatistics(): array
    {
        $totalFiles = FileSyncModel::where('sync_status', '!=', 3)->count();
        $totalSize = FileSyncModel::where('sync_status', '!=', 3)->sum('file_size') ?? 0;
        $totalProducts = FileSyncModel::where('sync_status', '!=', 3)
            ->select('product')
            ->groupBy('product')
            ->get()
            ->count();
        $totalSns = FileSyncModel::where('sync_status', '!=', 3)
            ->select('sn')
            ->groupBy('sn')
            ->get()
            ->count();
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_products' => $totalProducts,
            'total_sns' => $totalSns
        ];
    }
    
    /**
     * 获取产品列表
     */
    public function getProducts(int $page, int $pageSize): array
    {
        $result = FileProductModel::paginate($pageSize, ['*'], 'page', $page);
        
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'page_size' => $result->perPage(),
            'total_page' => $result->lastPage()
        ];
    }
    
    /**
     * 创建产品
     */
    public function createProduct(array $data): array
    {
        $product = new FileProductModel();
        $product->product_name = $data['product_name'];
        $product->product_code = $data['product_code'] ?? null;
        $product->description = $data['description'] ?? null;
        $product->status = 1;
        $product->save();
        
        return $product->toArray();
    }
    
    /**
     * 删除产品
     */
    public function deleteProduct(int $id): bool
    {
        $product = FileProductModel::find($id);
        
        if (!$product) {
            return false;
        }
        
        $product->delete();
        
        return true;
    }
    
    /**
     * 筛选老化测试文件
     */
    protected function filterAgingTestFiles(array $filters): array
    {
        $query = AgingTestResultModel::query();
        
        // 按运行时间筛选（单位：小时）
        if (isset($filters['runtime_hours_min'])) {
            $minSeconds = $filters['runtime_hours_min'] * 3600;
            $query->where('runtime_seconds', '>=', $minSeconds);
        }
        if (isset($filters['runtime_hours_max'])) {
            $maxSeconds = $filters['runtime_hours_max'] * 3600;
            $query->where('runtime_seconds', '<=', $maxSeconds);
        }
        
        return $query->pluck('file_sync_id')->toArray();
    }
    
    /**
     * 筛选厂测文件
     */
    protected function filterFactoryTestFiles(array $filters): array
    {
        $query = FactoryTestResultModel::query();
        
        // 厂测结果 (成功/失败)
        if (!empty($filters['factory_test_result'])) {
            $query->where('factory_test_result', $filters['factory_test_result']);
        }
        
        // 设备名
        if (!empty($filters['device_name'])) {
            $query->where('device_name', 'LIKE', '%' . $filters['device_name'] . '%');
        }
        
        // CPUID
        if (!empty($filters['cpuid'])) {
            $query->where('cpuid', 'LIKE', '%' . $filters['cpuid'] . '%');
        }
        
        // 厂测版本
        if (!empty($filters['factory_test_version'])) {
            $query->where('factory_test_version', 'LIKE', '%' . $filters['factory_test_version'] . '%');
        }
        
        // 固件版本
        if (!empty($filters['firmware_version'])) {
            $query->where('firmware_version', 'LIKE', '%' . $filters['firmware_version'] . '%');
        }
        
        // DDR
        if (!empty($filters['ddr'])) {
            $query->where('ddr', 'LIKE', '%' . $filters['ddr'] . '%');
        }
        
        // Flash
        if (!empty($filters['flash'])) {
            $query->where('flash', 'LIKE', '%' . $filters['flash'] . '%');
        }
        
        // 成功项目筛选
        if (!empty($filters['success_projects'])) {
            $query->where(function($q) use ($filters) {
                foreach ($filters['success_projects'] as $item) {
                    $q->where('success_projects', 'LIKE', '%' . $item . '%');
                }
            });
        }
        
        // 失败项目筛选
        if (!empty($filters['failed_projects'])) {
            $query->where(function($q) use ($filters) {
                foreach ($filters['failed_projects'] as $item) {
                    $q->where('failed_projects', 'LIKE', '%' . $item . '%');
                }
            });
        }
        
        // 测试项筛选 - 基于成功项目和失败项目字段（保留兼容）
        if (!empty($filters['test_items'])) {
            foreach ($filters['test_items'] as $item) {
                // 筛选成功项目中包含指定项的记录
                $query->where('success_projects', 'LIKE', '%' . $item . '%');
            }
        }
        
        return $query->pluck('file_sync_id')->toArray();
    }
    
    /**
     * 加载老化测试结果
     */
    protected function loadAgingTestResults(&$files): void
    {
        $fileIds = collect($files)->pluck('id')->toArray();
        if (empty($fileIds)) {
            return;
        }
        
        $results = AgingTestResultModel::whereIn('file_sync_id', $fileIds)
            ->get()
            ->keyBy('file_sync_id');
        
        foreach ($files as &$file) {
            if (isset($results[$file->id])) {
                $file->aging_test_result = $results[$file->id];
            }
        }
    }
    
    /**
     * 加载厂测结果
     */
    protected function loadFactoryTestResults(&$files): void
    {
        $fileIds = collect($files)->pluck('id')->toArray();
        if (empty($fileIds)) {
            return;
        }
        
        $results = FactoryTestResultModel::whereIn('file_sync_id', $fileIds)
            ->get()
            ->keyBy('file_sync_id');
        
        foreach ($files as &$file) {
            if (isset($results[$file->id])) {
                $file->factory_test_result = $results[$file->id];
            }
        }
    }
    
    /**
     * 获取老化测试统计数据
     */
    public function getAgingTestStatistics(array $params = []): array
    {
        $query = AgingTestResultModel::query();
        
        // 应用筛选条件
        if (!empty($params['product'])) {
            $query->where('product', $params['product']);
        }
        if (!empty($params['sn'])) {
            $query->where('sn', $params['sn']);
        }
        
        $total = $query->count();
        $avgSeconds = $query->avg('runtime_seconds') ?? 0;
        $maxSeconds = $query->max('runtime_seconds') ?? 0;
        $minSeconds = $query->min('runtime_seconds') ?? 0;
        
        $stats = [
            'total_count' => $total,
            'avg_runtime_hours' => round($avgSeconds / 3600, 2),
            'max_runtime_hours' => round($maxSeconds / 3600, 2),
            'min_runtime_hours' => round($minSeconds / 3600, 2),
            'avg_runtime_text' => $this->secondsToTimeString($avgSeconds),
            'max_runtime_text' => $this->secondsToTimeString($maxSeconds),
            'min_runtime_text' => $this->secondsToTimeString($minSeconds)
        ];
        
        return $stats;
    }
    
    /**
     * 将秒数转换为时间字符串格式
     */
    protected function secondsToTimeString(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
    }
    
    /**
     * 获取厂测统计数据
     */
    public function getFactoryTestStatistics(array $params = []): array
    {
        $query = FactoryTestResultModel::query();
        
        // 应用筛选条件
        if (!empty($params['product'])) {
            $query->where('product', $params['product']);
        }
        if (!empty($params['sn'])) {
            $query->where('sn', $params['sn']);
        }
        
        $total = $query->count();
        $success = (clone $query)->where('factory_test_result', '成功')->count();
        $failed = (clone $query)->where('factory_test_result', '失败')->count();
        
        $stats = [
            'total_count' => $total,
            'success_count' => $success,
            'failed_count' => $failed,
            'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0
        ];
        
        return $stats;
    }
    
    /**
     * 获取完整的树形目录数据（一次性加载所有层级）
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param string $search 搜索关键词
     * @param string $fileType 文件类型
     * @param array $agingFilters 老化测试筛选条件
     * @param array $factoryFilters 厂测筛选条件
     * @return array
     */
    public function getFullTreeData(
        array $filter = [],
        array $op = [],
        string $search = '',
        string $fileType = '',
        array $agingFilters = [],
        array $factoryFilters = []
    ): array
    {
        // 处理日期筛选
        $start = null;
        $end = null;
        if (isset($filter['start_date']) && isset($filter['end_date'])) {
            $start = Carbon::parse($filter['start_date'])->startOfDay()->toDateTimeString();
            $end = Carbon::parse($filter['end_date'])->endOfDay()->toDateTimeString();
        }
        
        // 构建基础查询
        $baseQuery = Db::table('file_sync')
            ->where('sync_status', '!=', 3);
        
        // 应用筛选条件
        if (!empty($filter['product'])) {
            $baseQuery->where('product', 'LIKE', '%' . $filter['product'] . '%');
        }
        
        if (!empty($filter['sn'])) {
            $baseQuery->where('sn', 'LIKE', '%' . $filter['sn'] . '%');
        }
        
        if ($start && $end) {
            $baseQuery->whereBetween('file_mtime', [$start, $end]);
        }
        
        // 文件类型筛选
        if (!empty($fileType)) {
            $baseQuery->where('file_type', $fileType);
        }
        
        // 应用搜索条件
        if (!empty($search)) {
            $baseQuery->where(function($query) use ($search) {
                $query->where('product', 'LIKE', "%{$search}%")
                      ->orWhere('sn', 'LIKE', "%{$search}%")
                      ->orWhere('test_datetime', 'LIKE', "%{$search}%");
            });
        }
        
        // 老化测试特有筛选条件
        if ($fileType === 'aging_test' && !empty($agingFilters)) {
            $fileSyncIds = $this->filterAgingTestFiles($agingFilters);
            if (!empty($fileSyncIds)) {
                $baseQuery->whereIn('id', $fileSyncIds);
            } else if ($this->hasAgingFilters($agingFilters)) {
                // 如果有筛选条件但没有匹配的文件，返回空结果
                return [
                    'tree' => [],
                    'total' => 0
                ];
            }
        }
        
        // 厂测特有筛选条件
        if ($fileType === 'factory_test' && !empty($factoryFilters)) {
            $fileSyncIds = $this->filterFactoryTestFiles($factoryFilters);
            if (!empty($fileSyncIds)) {
                $baseQuery->whereIn('id', $fileSyncIds);
            } else if ($this->hasFactoryFilters($factoryFilters)) {
                // 如果有筛选条件但没有匹配的文件，返回空结果
                return [
                    'tree' => [],
                    'total' => 0
                ];
            }
        }
        
        // 获取所有符合条件的数据
        $allFiles = $baseQuery
            ->select(['product', 'sn', 'test_datetime'])
            ->distinct()
            ->orderBy('product')
            ->orderBy('sn')
            ->orderByDesc('id')
            ->get();
        
        // 构建完整的三层树形结构
        $tree = [];
        $productGroups = $allFiles->groupBy('product');
        
        foreach ($productGroups as $product => $productFiles) {
            $productNode = [
                'id' => $product,
                'label' => $product,
                'type' => 'product',
                'children' => [],
                'isLeaf' => false
            ];
            
            $snGroups = $productFiles->groupBy('sn');
            foreach ($snGroups as $sn => $snFiles) {
                $snNode = [
                    'id' => $product . '/' . $sn,
                    'label' => $sn,
                    'type' => 'sn', 
                    'children' => [],
                    'isLeaf' => false
                ];
                
                foreach ($snFiles as $file) {
                    // 计算该日期节点下的文件数量
                    $fileCountQuery = (clone $baseQuery)
                        ->where('product', $product)
                        ->where('sn', $sn)
                        ->where('test_datetime', $file->test_datetime);
                    
                    $fileCount = $fileCountQuery->count();
                    
                    $dateNode = [
                        'id' => $product . '/' . $sn . '/' . $file->test_datetime,
                        'label' => $file->test_datetime,
                        'type' => 'date',
                        'children' => null,
                        'children_count' => $fileCount,
                        'isLeaf' => true
                    ];
                    
                    $snNode['children'][] = $dateNode;
                }
                
                // 计算SN节点下的总文件数量
                $snFileCountQuery = (clone $baseQuery)
                    ->where('product', $product)
                    ->where('sn', $sn);
                
                $snFileCount = $snFileCountQuery->count();
                $snNode['children_count'] = $snFileCount;
                
                $productNode['children'][] = $snNode;
            }
            
            // 计算产品节点下的总文件数量
            $productFileCountQuery = (clone $baseQuery)
                ->where('product', $product);
            
            $productFileCount = $productFileCountQuery->count();
            $productNode['children_count'] = $productFileCount;
            
            $tree[] = $productNode;
        }
        
        return [
            'tree' => $tree,
            'total' => count($tree)
        ];
    }
    
    /**
     * 检查是否有老化测试筛选条件
     */
    protected function hasAgingFilters(array $filters): bool
    {
        return !empty($filters) && (
            isset($filters['runtime_hours_min']) ||
            isset($filters['runtime_hours_max'])
        );
    }
    
    /**
     * 检查是否有厂测筛选条件
     */
    protected function hasFactoryFilters(array $filters): bool
    {
        return !empty($filters) && (
            !empty($filters['factory_test_result']) ||
            !empty($filters['device_name']) ||
            !empty($filters['cpuid']) ||
            !empty($filters['factory_test_version']) ||
            !empty($filters['firmware_version']) ||
            !empty($filters['ddr']) ||
            !empty($filters['flash']) ||
            !empty($filters['success_projects']) ||
            !empty($filters['failed_projects']) ||
            !empty($filters['test_items'])
        );
    }
    
    /**
     * 应用测试筛选条件
     * @param string $fileType 文件类型
     * @param array $agingFilters 老化测试筛选条件
     * @param array $factoryFilters 厂测筛选条件
     * @return array|null 返回符合条件的file_sync_id数组，或null表示没有筛选条件
     */
    protected function applyTestFilters(string $fileType, array $agingFilters, array $factoryFilters): ?array
    {
        if ($fileType === 'aging_test' && $this->hasAgingFilters($agingFilters)) {
            return $this->filterAgingTestFiles($agingFilters);
        }
        
        if ($fileType === 'factory_test' && $this->hasFactoryFilters($factoryFilters)) {
            return $this->filterFactoryTestFiles($factoryFilters);
        }
        
        return null;
    }
}
