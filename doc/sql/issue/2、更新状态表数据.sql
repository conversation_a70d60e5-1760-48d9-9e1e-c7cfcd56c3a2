-- ========================================
-- 第1步：设置事务开始
-- ========================================
-- ⚠️ 关键设置：防止自动提交
SET autocommit = 0;
-- 开始事务
START TRANSACTION;

-- =================================================================
-- 第1步：删除不需要的状态
-- =================================================================
DELETE FROM `issue_statuses`
WHERE statuses_type = 'system'
  AND id NOT IN (1, 2, 5, 6, 9, 10, 11, 12, 13, 15);

-- =================================================================
-- 第2步：为其他大类新建流程
-- =================================================================
-- 为缺陷类型新建缺失的状态（除了挂起ID=5）
INSERT INTO `issue_statuses` (`name`, `is_closed`, `position`, `default_done_ratio`, `project_id`, `statuses_type`, `is_active`, `status_category`, `tracker_type`) VALUES
('新建', 0, 1, NULL, NULL, 'system', 1, 'not_started', 'bug'),
('处理中', 0, 2, NULL, NULL, 'system', 1, 'in_progress', 'bug'),
('重开', 0, 3, NULL, NULL, 'system', 1, 'in_progress', 'bug'),
('待测试', 0, 4, NULL, NULL, 'system', 1, 'awaiting_acceptance', 'bug'),
('已修复', 1, 5, NULL, NULL, 'system', 1, 'completed', 'bug');

-- 为任务类型新建缺失的状态（已有未开始ID=11、进行中ID=12）
INSERT INTO `issue_statuses` (`name`, `is_closed`, `position`, `default_done_ratio`, `project_id`, `statuses_type`, `is_active`, `status_category`, `tracker_type`) VALUES
('已完成', 1, 3, NULL, NULL, 'system', 1, 'completed', 'task'),
('挂起', 0, 4, NULL, NULL, 'system', 1, 'in_progress', 'task');

-- 为产品类型新建所有状态
INSERT INTO `issue_statuses` (`name`, `is_closed`, `position`, `default_done_ratio`, `project_id`, `statuses_type`, `is_active`, `status_category`, `tracker_type`) VALUES
('未开始', 0, 1, NULL, NULL, 'system', 1, 'not_started', 'product'),
('进行中', 0, 2, NULL, NULL, 'system', 1, 'in_progress', 'product'),
('已完成', 1, 3, NULL, NULL, 'system', 1, 'completed', 'product');

--如无问题，请提交事务
-- select * from issue_statuses where statuses_type = 'system';
COMMIT;
SET autocommit = 1;  -- 恢复自动提交

-- 如果有问题请回滚
-- ROLLBACK;