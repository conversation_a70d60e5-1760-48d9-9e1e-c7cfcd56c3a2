-- ========================================
-- 第1步：先更新trackers表的tracker_type字段
-- ========================================
UPDATE `trackers` SET `name` = 'Defect', `description` = '缺陷跟进', `is_in_chlog` = 0, `position` = 10, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'bug' WHERE `id` = 2;
UPDATE `trackers` SET `name` = 'Feature', `description` = '新功能特性跟进', `is_in_chlog` = 0, `position` = 11, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 3;
UPDATE `trackers` SET `name` = 'Test', `description` = '固件/APP等测试跟进', `is_in_chlog` = 0, `position` = 12, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 4;
UPDATE `trackers` SET `name` = 'Support', `description` = '客户支持跟进', `is_in_chlog` = 0, `position` = 13, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 5;
UPDATE `trackers` SET `name` = 'FAQ', `description` = '', `is_in_chlog` = 0, `position` = 14, `is_in_roadmap` = 1, `fields_bits` = 248, `default_status_id` = 1, `tracker_type` = 'bug' WHERE `id` = 7;
UPDATE `trackers` SET `name` = '内容推广', `description` = '用于内容推广分发', `is_in_chlog` = 0, `position` = 4, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 8;
UPDATE `trackers` SET `name` = '需求', `description` = '产品需求标签', `is_in_chlog` = 0, `position` = 8, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 11;
UPDATE `trackers` SET `name` = '任务', `description` = '任务、子任务标签，为最终工作内容', `is_in_chlog` = 0, `position` = 9, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 12;
UPDATE `trackers` SET `name` = '产品上线', `description` = '产品上线项目标签', `is_in_chlog` = 0, `position` = 6, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 13;
UPDATE `trackers` SET `name` = '项目', `description` = '项目、子项目通用标签', `is_in_chlog` = 0, `position` = 7, `is_in_roadmap` = 1, `fields_bits` = 32, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 14;
UPDATE `trackers` SET `name` = '平面设计', `description` = '', `is_in_chlog` = 0, `position` = 3, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 15;
UPDATE `trackers` SET `name` = '视频设计', `description` = '基础视频设计', `is_in_chlog` = 0, `position` = 2, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 16;
UPDATE `trackers` SET `name` = '营销项目', `description` = '', `is_in_chlog` = 0, `position` = 5, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 17;
UPDATE `trackers` SET `name` = '内容创作', `description` = '', `is_in_chlog` = 0, `position` = 1, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 18;
UPDATE `trackers` SET `name` = '官网建设', `description` = '对官方网站的建议及反馈', `is_in_chlog` = 0, `position` = 15, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'task' WHERE `id` = 19;
UPDATE `trackers` SET `name` = '缺陷', `description` = '', `is_in_chlog` = 0, `position` = 16, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'bug' WHERE `id` = 20;
UPDATE `trackers` SET `name` = '研发流程', `description` = '', `is_in_chlog` = 0, `position` = 17, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 11, `tracker_type` = 'product' WHERE `id` = 21;
UPDATE `trackers` SET `name` = 'UI 调整', `description` = '', `is_in_chlog` = 0, `position` = 37, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 23;
UPDATE `trackers` SET `name` = '产品上线', `description` = '', `is_in_chlog` = 0, `position` = 47, `is_in_roadmap` = 1, `fields_bits` = 0, `default_status_id` = 1, `tracker_type` = 'requirement' WHERE `id` = 24;

-- ========================================
-- 第2步：更新自定义工作流的tracker_type(已按照时间为20250804数据库进行设置 请查看有没有新增的)
-- ========================================
UPDATE `workflow_templates` SET `project_id` = 44, `tracker_type` = 'requirement', `template_name` = 'Firefly产品上线', `description` = '', `created_by` = 51, `created_at` = '2025-07-18 17:13:29', `updated_at` = '2025-07-18 17:13:29', `deleted_at` = NULL WHERE `id` = 1;
UPDATE `workflow_templates` SET `project_id` = 205, `tracker_type` = 'bug', `template_name` = '缺陷工作流', `description` = '', `created_by` = 5, `created_at` = '2025-07-19 17:59:59', `updated_at` = '2025-07-19 17:59:59', `deleted_at` = NULL WHERE `id` = 2;
UPDATE `workflow_templates` SET `project_id` = 205, `tracker_type` = 'requirement', `template_name` = '需求工作流', `description` = '', `created_by` = 5, `created_at` = '2025-07-19 18:19:20', `updated_at` = '2025-07-28 16:50:43', `deleted_at` = NULL WHERE `id` = 3;
UPDATE `workflow_templates` SET `project_id` = 708, `tracker_type` = 'bug', `template_name` = '缺陷工作流', `description` = '', `created_by` = 5, `created_at` = '2025-07-21 19:55:18', `updated_at` = '2025-07-21 19:55:18', `deleted_at` = NULL WHERE `id` = 5;
UPDATE `workflow_templates` SET `project_id` = 708, `tracker_type` = 'requirement', `template_name` = '需求工作流', `description` = '', `created_by` = 5, `created_at` = '2025-07-21 19:57:57', `updated_at` = '2025-07-21 19:57:57', `deleted_at` = NULL WHERE `id` = 6;
UPDATE `workflow_templates` SET `project_id` = 662, `tracker_type` = 'requirement', `template_name` = '研发需求工作流（SR）', `description` = '', `created_by` = 5, `created_at` = '2025-07-28 15:24:12', `updated_at` = '2025-07-29 15:21:27', `deleted_at` = NULL WHERE `id` = 7;
UPDATE `workflow_templates` SET `project_id` = 662, `tracker_type` = 'bug', `template_name` = '缺陷工作流', `description` = '', `created_by` = 5, `created_at` = '2025-07-29 11:54:43', `updated_at` = '2025-07-29 11:54:43', `deleted_at` = NULL WHERE `id` = 8;

-- ========================================
-- 第3步：设置issue_statuses 为system的各个状态的tracker_type(已按照0804数据库进行设置 请务必查看有没有新增的)
-- ========================================
UPDATE `issue_statuses` SET `name` = '新建', `is_closed` = 0, `position` = 1, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'not_started', `tracker_type` = 'requirement' WHERE `id` = 1;
UPDATE `issue_statuses` SET `name` = '处理中', `is_closed` = 0, `position` = 3, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'in_progress', `tracker_type` = 'requirement' WHERE `id` = 2;
UPDATE `issue_statuses` SET `name` = '已解决', `is_closed` = 1, `position` = 9, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'completed', `tracker_type` = 'requirement' WHERE `id` = 3;
UPDATE `issue_statuses` SET `name` = '已关闭', `is_closed` = 1, `position` = 10, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'completed', `tracker_type` = NULL WHERE `id` = 4;
UPDATE `issue_statuses` SET `name` = '挂起', `is_closed` = 1, `position` = 11, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'completed', `tracker_type` = 'bug' WHERE `id` = 5;
UPDATE `issue_statuses` SET `name` = '重开', `is_closed` = 0, `position` = 5, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'in_progress', `tracker_type` = 'requirement' WHERE `id` = 6;
UPDATE `issue_statuses` SET `name` = '客户确认', `is_closed` = 0, `position` = 4, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'in_progress', `tracker_type` = NULL WHERE `id` = 7;
UPDATE `issue_statuses` SET `name` = '待验证', `is_closed` = 0, `position` = 6, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'awaiting_acceptance', `tracker_type` = 'requirement' WHERE `id` = 9;
UPDATE `issue_statuses` SET `name` = '待测试', `is_closed` = 0, `position` = 7, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'awaiting_acceptance', `tracker_type` = 'requirement' WHERE `id` = 10;
UPDATE `issue_statuses` SET `name` = '未开始', `is_closed` = 0, `position` = 12, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'not_started', `tracker_type` = 'task' WHERE `id` = 11;
UPDATE `issue_statuses` SET `name` = '进行中', `is_closed` = 0, `position` = 13, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'in_progress', `tracker_type` = 'task' WHERE `id` = 12;
UPDATE `issue_statuses` SET `name` = '已完成', `is_closed` = 1, `position` = 14, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'completed', `tracker_type` = 'requirement' WHERE `id` = 13;
UPDATE `issue_statuses` SET `name` = '待打包', `is_closed` = 0, `position` = 8, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'awaiting_acceptance', `tracker_type` = NULL WHERE `id` = 14;
UPDATE `issue_statuses` SET `name` = '需求规划', `is_closed` = 0, `position` = 2, `default_done_ratio` = NULL, `project_id` = NULL, `statuses_type` = 'system', `is_active` = 1, `status_category` = 'in_progress', `tracker_type` = 'requirement' WHERE `id` = 15;

-- ========================================
-- 第4步：备份表，请在Navicat中找到5张表，右键复制 -> 复制结构和数据进行备份
-- 1、issue_statuses
-- 2、issues
-- 3、workflows
-- 4、custom_workflows
-- 5、trackers
-- ========================================

-- ========================================
-- 第5步：状态映射表，如已使用程序迁移，请忽略
-- ========================================
-- 创建状态映射表 后续更新issues、workflows、custom_workflows进行状态映射
-- CREATE TABLE IF NOT EXISTS `status_mapping` (
--   `old_status_id` INT(11) NOT NULL,
--   `tracker_type` VARCHAR(50) NOT NULL,
--   `new_status_id` INT(11) NOT NULL,
--   PRIMARY KEY (`old_status_id`, `tracker_type`)
-- );