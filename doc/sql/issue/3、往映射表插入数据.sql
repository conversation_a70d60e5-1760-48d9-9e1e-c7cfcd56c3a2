-- ========================================
-- 状态映射表数据插入
-- ========================================

-- 设置变量（基于线上实际ID）!!!请注意一定要检查 @req_xxx表示需求类型 @bug_xxx表示缺陷类型 @task_xxx表示任务类型 @product_xxx表示产品类型
-- 需求类型使用的现有ID
SET @req_new_id = 1;           -- 新建
SET @req_processing_id = 2;    -- 处理中
SET @req_reopen_id = 6;        -- 重开
SET @req_verification_id = 9;  -- 待验证
SET @req_testing_id = 10;      -- 待测试
SET @req_completed_id = 13;    -- 已完成
SET @req_planning_id = 15;     -- 需求规划

-- 缺陷类型使用的现有ID和新ID
SET @bug_suspended_id = 5;     -- 保留的现有ID（挂起）
SET @bug_new_id = (SELECT id FROM issue_statuses WHERE name = '新建' AND tracker_type = 'bug' AND statuses_type = 'system' LIMIT 1);
SET @bug_processing_id = (SELECT id FROM issue_statuses WHERE name = '处理中' AND tracker_type = 'bug' AND statuses_type = 'system' LIMIT 1);
SET @bug_reopen_id = (SELECT id FROM issue_statuses WHERE name = '重开' AND tracker_type = 'bug' AND statuses_type = 'system' LIMIT 1);
SET @bug_testing_id = (SELECT id FROM issue_statuses WHERE name = '待测试' AND tracker_type = 'bug' AND statuses_type = 'system' LIMIT 1);
SET @bug_fixed_id = (SELECT id FROM issue_statuses WHERE name = '已修复' AND tracker_type = 'bug' AND statuses_type = 'system' LIMIT 1);

-- 任务类型使用的现有ID和新ID
SET @task_not_started_id = 11; -- 保留的现有ID（未开始）
SET @task_in_progress_id = 12; -- 保留的现有ID（进行中）
SET @task_completed_id = (SELECT id FROM issue_statuses WHERE name = '已完成' AND tracker_type = 'task' AND statuses_type = 'system' LIMIT 1);
SET @task_suspended_id = (SELECT id FROM issue_statuses WHERE name = '挂起' AND tracker_type = 'task' AND statuses_type = 'system' LIMIT 1);

-- 产品类型的新ID
SET @product_not_started_id = (SELECT id FROM issue_statuses WHERE name = '未开始' AND tracker_type = 'product' AND statuses_type = 'system' LIMIT 1);
SET @product_in_progress_id = (SELECT id FROM issue_statuses WHERE name = '进行中' AND tracker_type = 'product' AND statuses_type = 'system' LIMIT 1);
SET @product_completed_id = (SELECT id FROM issue_statuses WHERE name = '已完成' AND tracker_type = 'product' AND statuses_type = 'system' LIMIT 1);

-- 根据线上实际情况填充映射关系(用于批量更新issues表和workflows表中的状态引用)
-- 举例：
-- 旧状态ID=7（客户确认）→ 需求类型转为ID=2（处理中）
-- 旧状态ID=7（客户确认）→ 缺陷类型转为新建的"处理中"状态
-- 旧状态ID=7（客户确认）→ 任务类型转为ID=12（进行中）
INSERT INTO `status_mapping` (`old_status_id`, `tracker_type`, `new_status_id`) VALUES
-- 需求类型映射（基于线上实际ID）
(1, 'requirement', @req_new_id),        -- id1新建 → 需求新建
(11, 'requirement', @req_new_id),       -- id11未开始 → 需求新建
(15, 'requirement', @req_planning_id),  -- id15需求规划 → 需求规划
(7, 'requirement', @req_processing_id), -- id7客户确认 → 需求处理中
(2, 'requirement', @req_processing_id), -- id2处理中 → 需求处理中
(12, 'requirement', @req_processing_id),-- id12进行中 → 需求处理中
(6, 'requirement', @req_reopen_id),     -- id6重开 → 需求重开
(9, 'requirement', @req_verification_id),-- id9待验证 → 需求待验证
(10, 'requirement', @req_testing_id),   -- id10待测试 → 需求待测试
(14, 'requirement', @req_testing_id),   -- id14待打包 → 需求待测试
(13, 'requirement', @req_completed_id), -- id13已完成 → 需求已完成
(3, 'requirement', @req_completed_id),  -- id3已解决 → 需求已完成
(4, 'requirement', @req_completed_id),  -- id4已关闭 → 需求已完成
(5, 'requirement', @req_completed_id),  -- id5挂起 → 需求已完成

-- 缺陷类型映射
(1, 'bug', @bug_new_id),                -- id1新建 → 缺陷新建
(11, 'bug', @bug_new_id),                -- id11未开始 → 缺陷新建
(15, 'bug', @bug_processing_id),        -- id15需求规划 → 缺陷处理中
(7, 'bug', @bug_processing_id),         -- id7客户确认 → 缺陷处理中
(2, 'bug', @bug_processing_id),         -- id2处理中 → 缺陷处理中
(12, 'bug', @bug_processing_id),        -- id12进行中 → 缺陷处理中
(6, 'bug', @bug_reopen_id),             -- id6重开 → 缺陷重开
(9, 'bug', @bug_testing_id),            -- id9待验证 → 缺陷待测试
(10, 'bug', @bug_testing_id),           -- id10待测试 → 缺陷待测试
(14, 'bug', @bug_testing_id),           -- id14待打包 → 缺陷待测试
(13, 'bug', @bug_fixed_id),             -- id13已完成 → 缺陷已修复
(3, 'bug', @bug_fixed_id),              -- id3已解决 → 缺陷已修复
(4, 'bug', @bug_fixed_id),              -- id4已关闭 → 缺陷已修复
(5, 'bug', @bug_suspended_id),          -- id5挂起 → 缺陷挂起

-- 任务类型映射
(1, 'task', @task_not_started_id),      -- id1新建 → 任务未开始
(11, 'task', @task_not_started_id),     -- id11未开始 → 任务未开始
(15, 'task', @task_in_progress_id),     -- id15需求规划 → 任务进行中
(7, 'task', @task_in_progress_id),      -- id7客户确认 → 任务进行中
(2, 'task', @task_in_progress_id),      -- id2处理中 → 任务进行中
(12, 'task', @task_in_progress_id),     -- id12进行中 → 任务进行中
(6, 'task', @task_in_progress_id),      -- id6重开 → 任务进行中
(9, 'task', @task_in_progress_id),      -- id9待验证 → 任务进行中
(10, 'task', @task_in_progress_id),     -- id10待测试 → 任务进行中
(14, 'task', @task_in_progress_id),     -- id14待打包 → 任务进行中
(13, 'task', @task_completed_id),       -- id13已完成 → 任务已完成
(3, 'task', @task_completed_id),        -- id3已解决 → 任务已完成
(4, 'task', @task_completed_id),        -- id4已关闭 → 任务已完成
(5, 'task', @task_suspended_id),        -- id5挂起 → 任务挂起

-- 产品类型映射
(1, 'product', @product_not_started_id),-- id1新建 → 产品未开始
(11, 'product', @product_not_started_id),-- id11未开始 → 产品未开始
(15, 'product', @product_in_progress_id),-- id15需求规划 → 产品进行中
(7, 'product', @product_in_progress_id), -- id7客户确认 → 产品进行中
(2, 'product', @product_in_progress_id), -- id2处理中 → 产品进行中
(12, 'product', @product_in_progress_id),-- id12进行中 → 产品进行中
(6, 'product', @product_in_progress_id), -- id6重开 → 产品进行中
(9, 'product', @product_in_progress_id), -- id9待验证 → 产品进行中
(10, 'product', @product_in_progress_id),-- id10待测试 → 产品进行中
(14, 'product', @product_in_progress_id),-- id14待打包 → 产品进行中
(13, 'product', @product_completed_id),  -- id13已完成 → 产品已完成
(3, 'product', @product_completed_id),   -- id3已解决 → 产品已完成
(4, 'product', @product_completed_id),   -- id4已关闭 → 产品已完成
(5, 'product', @product_completed_id);   -- id5挂起 → 产品已完成