# 事项状态重构SQL执行说明

## 📋 概述

本文档详细说明了事项状态系统重构的SQL脚本逻辑、执行步骤和注意事项。该重构旨在将原有的混合状态系统改造为按事项类型（requirement/bug/task/product）分类的清晰状态体系。

## 🎯 重构目标

### 问题背景
- **状态混乱**：原系统所有事项类型共用一套状态，导致状态语义不清
- **业务不匹配**：不同事项类型的业务流程差异很大，但使用相同状态名称
- **维护困难**：状态修改影响所有事项类型，难以针对性优化

### 解决方案
- **分类管理**：为每种事项类型定义专属状态集合
- **语义清晰**：状态名称与业务流程高度匹配
- **独立维护**：各事项类型状态可独立调整，互不影响

## 🏗️ 数据库结构分析

### 核心表结构

#### 1. `issue_statuses` - 状态表
```sql
-- 关键字段
id                  INT(11)      -- 状态ID（主键）
name                VARCHAR(255) -- 状态名称
is_closed           TINYINT(1)   -- 是否关闭状态
position            INT(11)      -- 显示顺序
default_done_ratio  INT(11)      -- 默认完成比例
project_id          INT(11)      -- 项目ID（NULL=系统级）
statuses_type       VARCHAR(50)  -- 状态类型（system/custom）
is_active           TINYINT(1)   -- 是否激活
status_category     VARCHAR(50)  -- 状态分类
tracker_type        VARCHAR(50)  -- 关联事项类型 ⭐新增字段
```

#### 2. `issues` - 事项表
```sql
-- 关键字段
id          INT(11) -- 事项ID
tracker_id  INT(11) -- 事项类型ID（关联trackers表）
status_id   INT(11) -- 状态ID（关联issue_statuses表）⭐需要更新
done_ratio  INT(11) -- 完成比例 ⭐需要同步更新
```

#### 3. `trackers` - 事项类型表
```sql
-- 关键字段
id           INT(11)     -- 类型ID
name         VARCHAR(255) -- 类型名称
tracker_type VARCHAR(50)  -- 类型标识（requirement/bug/task/product）
```

#### 4. `custom_workflows` - 自定义工作流表
```sql
-- 关键字段
project_id     INT(11) -- 项目ID
tracker_type   VARCHAR(50) -- 事项类型 ⭐关键关联字段
old_status_id  INT(11) -- 源状态ID ⭐需要更新
new_status_id  INT(11) -- 目标状态ID ⭐需要更新
```

#### 5. `workflows` - 系统工作流表
```sql
-- 关键字段
tracker_id     INT(11) -- 事项类型ID（通过此字段获取tracker_type）
old_status_id  INT(11) -- 源状态ID ⭐需要更新
new_status_id  INT(11) -- 目标状态ID ⭐需要更新
```

## 📊 新状态体系设计

### 需求类型（requirement）
| 状态名称 | 完成比例 | 状态分类 | 业务含义 |
|---------|---------|---------|---------|
| 新建 | 0% | not_started | 需求刚创建，待处理 |
| 需求规划 | 20% | in_progress | 需求分析和规划阶段 |
| 处理中 | 50% | in_progress | 开发实现阶段 |
| 重开 | 30% | in_progress | 需求被重新打开 |
| 待验证 | 80% | awaiting_acceptance | 等待产品验证 |
| 待测试 | 90% | awaiting_acceptance | 等待测试验证 |
| 已完成 | 100% | completed | 需求已完成并验收 |

### 缺陷类型（bug）
| 状态名称 | 完成比例 | 状态分类 | 业务含义 |
|---------|---------|---------|---------|
| 新建 | 0% | not_started | 缺陷刚报告，待处理 |
| 处理中 | 50% | in_progress | 正在修复缺陷 |
| 重开 | 30% | in_progress | 缺陷被重新打开 |
| 待测试 | 80% | awaiting_acceptance | 等待测试验证修复 |
| 已修复 | 100% | completed | 缺陷已修复并验证 |
| 挂起 | 50% | in_progress | 缺陷暂时挂起处理 |

### 任务类型（task）
| 状态名称 | 完成比例 | 状态分类 | 业务含义 |
|---------|---------|---------|---------|
| 未开始 | 0% | not_started | 任务尚未开始 |
| 进行中 | 50% | in_progress | 任务正在执行 |
| 已完成 | 100% | completed | 任务已完成 |
| 挂起 | 50% | in_progress | 任务暂时挂起 |

### 产品类型（product）
| 状态名称 | 完成比例 | 状态分类 | 业务含义 |
|---------|---------|---------|---------|
| 未开始 | 0% | not_started | 产品开发尚未开始 |
| 进行中 | 50% | in_progress | 产品正在开发 |
| 已完成 | 100% | completed | 产品开发已完成 |

## 🔄 执行流程详解

### 执行顺序（严格按序）
```
0、备份.sql → 1、替换系统状态表.sql → 2、创建状态映射表.sql → 
3、更新事项表.sql → 4、更新自定义工作流表.sql → 5、更新系统工作流表.sql
```

### 各步骤详细说明

#### 步骤0：数据备份
**目的**：为所有关键表创建备份，确保可以回滚
**操作**：
- 备份 `issue_statuses` → `issue_statuses_backup_20250804`
- 备份 `trackers` → `trackers_backup_20250804`
- 备份 `issues` → `issues_backup_20250804`
- 备份 `custom_workflows` → `custom_workflows_backup_20250804`
- 备份 `workflows` → `workflows_backup_20250804`

#### 步骤1：替换系统状态表
**目的**：删除旧的系统状态，插入新的分类状态
**关键逻辑**：
```sql
-- 只删除系统状态，保护custom状态
DELETE FROM issue_statuses 
WHERE tracker_type IN ('requirement', 'bug', 'task', 'product') 
  AND statuses_type = 'system';

-- 插入新的分类状态（按事项类型分组）
INSERT INTO issue_statuses (...) VALUES
('新建', 0, 1, 0, NULL, 'system', 1, 'not_started', 'requirement'),
...
```

#### 步骤2：创建状态映射表
**目的**：建立旧状态ID到新状态ID的映射关系
**核心表结构**：
```sql
CREATE TABLE status_mapping (
  old_status_id INT(11) NOT NULL,    -- 旧状态ID
  tracker_type VARCHAR(50) NOT NULL, -- 事项类型
  new_status_id INT(11) NOT NULL,    -- 新状态ID
  PRIMARY KEY (old_status_id, tracker_type)
);
```

**⚠️ 重要提醒**：文件中的新状态ID（101、201等）是示例，需要替换为步骤1执行后的实际ID！

#### 步骤3：更新事项表
**目的**：将所有事项的status_id从旧ID更新为新ID
**核心逻辑**：
```sql
-- 通过事项类型+旧状态ID匹配新状态ID
UPDATE issues AS i
JOIN trackers AS t ON i.tracker_id = t.id
JOIN status_mapping AS m 
  ON i.status_id = m.old_status_id 
  AND t.tracker_type = m.tracker_type
SET i.status_id = m.new_status_id;
```

#### 步骤4：更新自定义工作流表
**目的**：更新自定义工作流中的状态ID引用
**处理字段**：`old_status_id` 和 `new_status_id`

#### 步骤5：更新系统工作流表
**目的**：更新系统工作流中的状态ID引用
**特点**：通过 `tracker_id` 关联获取事项类型

## ⚠️ 执行注意事项

### 执行前准备
1. **测试环境验证**：必须先在测试环境完整执行一遍
2. **获取实际ID**：执行步骤1后，查询新状态的实际ID
3. **更新映射文件**：将步骤2中的示例ID替换为实际ID
4. **数据库备份**：生产环境执行前进行完整备份

### ID获取查询
```sql
-- 获取新状态的实际ID
SELECT id, name, tracker_type 
FROM issue_statuses 
WHERE statuses_type = 'system' 
ORDER BY tracker_type, position;
```

### 验证查询
```sql
-- 验证事项状态更新是否正确
SELECT 
  i.id,
  t.tracker_type,
  i.status_id,
  s.name AS status_name
FROM issues i
JOIN trackers t ON i.tracker_id = t.id
JOIN issue_statuses s ON i.status_id = s.id
WHERE t.tracker_type IN ('requirement', 'bug', 'task', 'product')
LIMIT 20;
```

## 🔍 Model字段验证

基于代码分析，SQL脚本与Model定义完全匹配：

### ✅ 字段对应关系验证
- `IssueStatusModel`：所有字段都在SQL中正确使用
- `IssueModel`：`status_id`、`done_ratio` 字段更新逻辑正确
- `TrackersModel`：`tracker_type` 字段用于关联匹配
- `CustomWorkflowModel`：`tracker_type`、状态ID字段更新逻辑正确
- `WorkflowsModel`：通过 `tracker_id` 获取类型的逻辑正确

### ✅ 关联关系验证
- 事项→状态：`issues.status_id` → `issue_statuses.id`
- 事项→类型：`issues.tracker_id` → `trackers.id`
- 工作流→状态：正确处理了新旧状态ID的更新

## 🚀 执行建议

### 推荐执行方式
1. **分步执行**：逐个文件执行，每步验证后再继续
2. **ID自动化**：建议编写脚本自动获取新ID并更新映射表
3. **回滚准备**：每步都保持事务状态，出错立即回滚

### 风险控制
- 所有操作都在事务中进行，支持完整回滚
- 备份表保留了所有原始数据
- 验证查询确保每步执行正确

这套SQL方案设计完善，与现有Model结构完全兼容，可以安全执行！

## 📝 执行检查清单

### 执行前检查
- [ ] 测试环境已完整验证
- [ ] 生产数据库已完整备份
- [ ] 步骤2映射文件中的ID已更新为实际值
- [ ] 所有相关服务已停止（避免并发修改）
- [ ] 数据库连接已切换到维护模式

### 执行中检查
- [ ] 步骤0：备份表创建成功，数据完整
- [ ] 步骤1：新状态插入成功，custom状态未受影响
- [ ] 步骤2：映射表创建成功，所有旧状态都有对应关系
- [ ] 步骤3：事项状态更新成功，done_ratio同步正确
- [ ] 步骤4：自定义工作流更新成功，无遗漏记录
- [ ] 步骤5：系统工作流更新成功，验证查询返回空

### 执行后验证
- [ ] 前端页面状态显示正常
- [ ] 工作流流转功能正常
- [ ] 事项创建和编辑功能正常
- [ ] 状态筛选功能正常
- [ ] 报表统计数据正确

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 映射表ID不匹配
**现象**：步骤3执行后部分事项状态未更新
**原因**：步骤2中的示例ID未替换为实际ID
**解决**：
```sql
-- 查询实际的新状态ID
SELECT id, name, tracker_type FROM issue_statuses
WHERE statuses_type = 'system'
ORDER BY tracker_type, position;

-- 更新映射表
UPDATE status_mapping SET new_status_id = 实际ID WHERE old_status_id = X AND tracker_type = 'requirement';
```

#### 2. 事务超时
**现象**：大表更新时事务超时
**解决**：
```sql
-- 分批更新，每次处理1000条
UPDATE issues AS i
JOIN trackers AS t ON i.tracker_id = t.id
JOIN status_mapping AS m ON i.status_id = m.old_status_id AND t.tracker_type = m.tracker_type
SET i.status_id = m.new_status_id
WHERE i.id BETWEEN 1 AND 1000;
```

#### 3. 外键约束错误
**现象**：删除旧状态时报外键约束错误
**解决**：检查是否有其他表引用了旧状态ID，需要先更新引用表

#### 4. Custom状态意外删除
**现象**：项目自定义状态被误删
**解决**：从备份表恢复
```sql
INSERT INTO issue_statuses
SELECT * FROM issue_statuses_backup_20250804
WHERE statuses_type = 'custom';
```

## 📊 性能优化建议

### 大数据量处理
如果 `issues` 表数据量很大（>100万条），建议：

1. **分批处理**：
```sql
-- 按tracker_type分批更新
UPDATE issues AS i
JOIN trackers AS t ON i.tracker_id = t.id
JOIN status_mapping AS m ON i.status_id = m.old_status_id AND t.tracker_type = m.tracker_type
SET i.status_id = m.new_status_id
WHERE t.tracker_type = 'requirement';  -- 逐个类型处理
```

2. **添加临时索引**：
```sql
-- 执行前添加
ALTER TABLE issues ADD INDEX temp_idx_tracker_status (tracker_id, status_id);
-- 执行后删除
ALTER TABLE issues DROP INDEX temp_idx_tracker_status;
```

3. **监控进度**：
```sql
-- 查看更新进度
SELECT
  t.tracker_type,
  COUNT(*) as total,
  SUM(CASE WHEN EXISTS(SELECT 1 FROM status_mapping m WHERE i.status_id = m.new_status_id AND t.tracker_type = m.tracker_type) THEN 1 ELSE 0 END) as updated
FROM issues i
JOIN trackers t ON i.tracker_id = t.id
WHERE t.tracker_type IN ('requirement', 'bug', 'task', 'product')
GROUP BY t.tracker_type;
```

## 🔄 回滚方案

如果执行过程中出现问题，可以使用以下回滚方案：

### 完整回滚
```sql
-- 1. 回滚事项表
UPDATE issues AS i
JOIN issues_backup_20250804 AS b ON i.id = b.id
SET i.status_id = b.status_id, i.done_ratio = b.done_ratio;

-- 2. 回滚状态表
DELETE FROM issue_statuses WHERE statuses_type = 'system';
INSERT INTO issue_statuses SELECT * FROM issue_statuses_backup_20250804;

-- 3. 回滚工作流表
DELETE FROM custom_workflows;
INSERT INTO custom_workflows SELECT * FROM custom_workflows_backup_20250804;

DELETE FROM workflows;
INSERT INTO workflows SELECT * FROM workflows_backup_20250804;

-- 4. 清理临时表
DROP TABLE status_mapping;
DROP TABLE system_statuses_backup;
```

### 部分回滚
如果只需要回滚特定步骤，可以使用对应的备份表进行恢复。

## 📈 后续维护

### 缓存清理
执行完成后需要清理相关缓存：
```php
// 清理状态缓存
Cache::forget('issue_status');
Cache::forget('workflow_*');

// 或者重启相关服务
```

### 监控指标
建议监控以下指标确保系统正常：
- 各状态的事项数量分布
- 工作流流转成功率
- 页面响应时间
- 错误日志数量

---

**最终提醒**：这是一个重要的数据结构变更，务必在测试环境充分验证后再在生产环境执行！
