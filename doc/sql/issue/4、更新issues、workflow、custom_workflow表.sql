-- ========================================
-- 第1步：设置事务开始
-- ========================================
-- ⚠️ 关键设置：防止自动提交
SET autocommit = 0;
-- 开始事务
START TRANSACTION;

-- =================================================================
-- 第1步：更新issues 事项表
-- =================================================================
UPDATE `issues` AS i
JOIN `trackers` AS t ON i.`tracker_id` = t.`id`  -- 获取事项类型
JOIN `status_mapping` AS m
  ON i.`status_id` = m.`old_status_id`
  AND t.`tracker_type` = m.`tracker_type`  -- 匹配事项类型和旧状态
SET i.`status_id` = m.`new_status_id`;  -- 更新为新状态ID



-- =================== 请执行完第1步再执行2步 ===================



-- =================================================================
-- 第2步：更新工作流表（workflows表）
-- =================================================================
-- 第2.1步：更新old_status_id
UPDATE workflows w
JOIN trackers t ON w.tracker_id = t.id
JOIN status_mapping m ON w.old_status_id = m.old_status_id AND t.tracker_type = m.tracker_type
SET w.old_status_id = m.new_status_id;

-- 第2.2步：更新new_status_id
UPDATE workflows w
JOIN trackers t ON w.tracker_id = t.id
JOIN status_mapping m ON w.new_status_id = m.old_status_id AND t.tracker_type = m.tracker_type
SET w.new_status_id = m.new_status_id;



-- =================== 请执行完第2步再执行3步 ===================



-- =================================================================
-- 第3步：更新工作流表（custom_workflows表）
-- =================================================================
-- 第3.1步：先设置custom_workflows的tracker_type（通过work_templates的template_id关联）
UPDATE custom_workflows cw
JOIN workflow_templates wt ON cw.template_id = wt.id
SET cw.tracker_type = wt.tracker_type
WHERE cw.tracker_type IS NULL
  AND cw.template_id IS NOT NULL
  AND wt.tracker_type IS NOT NULL;

-- 第3.2步：更新custom_workflows的old_status_id（只更新系统状态）
UPDATE custom_workflows cw
JOIN status_mapping m ON cw.old_status_id = m.old_status_id AND cw.tracker_type = m.tracker_type
JOIN issue_statuses s ON m.old_status_id = s.id
SET cw.old_status_id = m.new_status_id
WHERE s.statuses_type = 'system';  -- 只更新系统状态，跳过自定义状态


-- 第3.3步：更新custom_workflows的new_status_id（只更新系统状态）
UPDATE custom_workflows cw
JOIN status_mapping m ON cw.new_status_id = m.old_status_id AND cw.tracker_type = m.tracker_type
JOIN issue_statuses s ON m.old_status_id = s.id
SET cw.new_status_id = m.new_status_id
WHERE s.statuses_type = 'system';  -- 只更新系统状态，跳过自定义状态



-- =================== 请执行完第3步验证后再执行事务提交 ===================

-- ⚠️⚠️⚠️⚠️⚠️ 查看具体issues的异常事项 ⚠️⚠️⚠️⚠️⚠️
-- SELECT 
--   i.id as issue_id,
--   i.status_id as invalid_status_id,
--   i.tracker_id as '事项id',
-- 	 t.`name` as '事项名称',
--   t.tracker_type,
--   '找不到对应状态' as problem
-- FROM issues i
-- LEFT JOIN issue_statuses s ON i.status_id = s.id
-- LEFT JOIN trackers t ON i.tracker_id = t.id
-- WHERE s.id IS NULL

-- ⚠️⚠️⚠️⚠️⚠️ 检查系统工作流表 - 统计使用的状态 如出现null 可能是旧流程如Defect Feature那些 ⚠️⚠️⚠️⚠️⚠️
-- SELECT
--   'workflows状态统计' as table_name,
--   t.tracker_type,
--   s.id as status_id,
--   s.name as status_name,
--   COUNT(DISTINCT w.id) as workflow_count
-- FROM `workflows` w
-- LEFT JOIN `trackers` t ON w.tracker_id = t.id
-- LEFT JOIN `issue_statuses` s ON (w.old_status_id = s.id OR w.new_status_id = s.id)
-- WHERE s.id IS NOT NULL
-- GROUP BY t.tracker_type, s.id, s.name
-- ORDER BY t.tracker_type, workflow_count DESC;

-- ⚠️⚠️⚠️⚠️⚠️ 检查自定义工作流表 - 统计使用的状态 ⚠️⚠️⚠️⚠️⚠️
-- SELECT
--   'custom_workflows状态统计' as table_name,
--   cw.tracker_type,
--   s.id as status_id,
--   s.name as status_name,
--   COUNT(DISTINCT cw.id) as workflow_count
-- FROM `custom_workflows` cw
-- LEFT JOIN `issue_statuses` s ON (cw.old_status_id = s.id OR cw.new_status_id = s.id)
-- WHERE s.id IS NOT NULL
-- GROUP BY cw.tracker_type, s.id, s.name
-- ORDER BY cw.tracker_type, workflow_count DESC;

--如无问题，请提交事务
COMMIT;
SET autocommit = 1;  -- 恢复自动提交

-- 如果有问题请回滚
-- ROLLBACK;